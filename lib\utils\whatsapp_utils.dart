import 'package:url_launcher/url_launcher.dart';

class WhatsAppUtils {
  /// Opens WhatsApp with a specific phone number
  /// Phone number should include country code (e.g., +33 for France)
  static Future<bool> openWhatsApp(String phoneNumber, {String? message}) async {
    // Clean and format the phone number
    String cleanedPhone = _cleanPhoneNumber(phoneNumber);
    
    if (cleanedPhone.isEmpty) {
      return false;
    }
    
    // Construct WhatsApp URL
    String whatsappUrl = 'https://wa.me/$cleanedPhone';
    if (message != null && message.isNotEmpty) {
      whatsappUrl += '?text=${Uri.encodeComponent(message)}';
    }
    
    try {
      final Uri uri = Uri.parse(whatsappUrl);
      if (await canLaunchUrl(uri)) {
        return await launchUrl(uri, mode: LaunchMode.externalApplication);
      }
    } catch (e) {
      // If web link fails, try opening WhatsApp directly
      try {
        final Uri whatsappUri = Uri.parse('whatsapp://send?phone=$cleanedPhone${message != null ? '&text=${Uri.encodeComponent(message)}' : ''}');
        if (await canLaunchUrl(whatsappUri)) {
          return await launchUrl(whatsappUri);
        }
      } catch (e) {
        return false;
      }
    }
    
    return false;
  }
  
  /// Cleans and formats a phone number for WhatsApp
  /// Removes all non-numeric characters and adds country code if missing
  static String _cleanPhoneNumber(String phoneNumber) {
    // Remove all non-numeric characters except +
    String cleaned = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');
    
    if (cleaned.isEmpty) return '';
    
    // If it starts with +, keep it
    if (cleaned.startsWith('+')) {
      return cleaned;
    }
    
    // If it starts with 00, replace with +
    if (cleaned.startsWith('00')) {
      return '+${cleaned.substring(2)}';
    }
    
    // If it's a French mobile number (starts with 06, 07), add +33
    if (cleaned.startsWith('0') && cleaned.length == 10) {
      if (cleaned.startsWith('06') || cleaned.startsWith('07')) {
        return '+33${cleaned.substring(1)}';
      }
    }
    
    // If it doesn't start with 0 and is 9 digits, assume it's French mobile without leading 0
    if (!cleaned.startsWith('0') && cleaned.length == 9) {
      return '+33$cleaned';
    }
    
    // If it starts with a country code but no +, add it
    if (cleaned.length >= 10 && !cleaned.startsWith('0')) {
      return '+$cleaned';
    }
    
    return cleaned;
  }
  
  /// Validates if a phone number could be a valid WhatsApp number
  static bool isValidWhatsAppNumber(String phoneNumber) {
    String cleaned = _cleanPhoneNumber(phoneNumber);
    return cleaned.startsWith('+') && cleaned.length >= 8;
  }
  
  /// Formats phone number for display
  static String formatPhoneForDisplay(String phoneNumber) {
    String cleaned = _cleanPhoneNumber(phoneNumber);
    
    if (cleaned.startsWith('+33') && cleaned.length == 12) {
      // French number formatting: +33 6 12 34 56 78
      return '${cleaned.substring(0, 3)} ${cleaned.substring(3, 4)} ${cleaned.substring(4, 6)} ${cleaned.substring(6, 8)} ${cleaned.substring(8, 10)} ${cleaned.substring(10)}';
    }
    
    return phoneNumber; // Return original if can't format
  }
}
