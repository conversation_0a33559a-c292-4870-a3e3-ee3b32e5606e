import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../providers/app_provider.dart';
import '../../models/class.dart';
import '../../models/student.dart';
import '../../models/course.dart';
import '../../models/grading_system.dart';
import '../../utils/app_theme.dart';

class AddCompetencyGradeScreen extends StatefulWidget {
  final String? studentId;
  final String? classId;

  const AddCompetencyGradeScreen({
    super.key,
    this.studentId,
    this.classId,
  });

  @override
  State<AddCompetencyGradeScreen> createState() => _AddCompetencyGradeScreenState();
}

class _AddCompetencyGradeScreenState extends State<AddCompetencyGradeScreen> {
  final _formKey = GlobalKey<FormState>();
  final _competencyController = TextEditingController();
  final _notesController = TextEditingController();
  final _contextController = TextEditingController();

  Student? _selectedStudent;
  String? _selectedSubject;
  CompetencyLevel? _selectedLevel;
  DateTime _evaluationDate = DateTime.now();
  bool _isLoading = false;
  List<Student> _primaryStudents = [];

  // Common competencies by subject for French primary education
  final Map<String, List<String>> _commonCompetencies = {
    'Français': [
      'Comprendre un texte lu par l\'enseignant',
      'Lire à voix haute avec fluidité',
      'Comprendre un texte écrit',
      'Produire des écrits variés',
      'Réviser et améliorer l\'écrit',
      'Connaître les correspondances graphophonologiques',
      'Mémoriser et se remémorer l\'orthographe de mots fréquents',
      'Raisonner pour résoudre des problèmes orthographiques',
      'Identifier les constituants d\'une phrase simple',
      'Distinguer le présent, le passé et le futur',
    ],
    'Mathématiques': [
      'Comprendre et utiliser des nombres entiers',
      'Nommer, lire, écrire, représenter des nombres entiers',
      'Résoudre des problèmes en utilisant des nombres entiers',
      'Calculer avec des nombres entiers',
      'Comparer, estimer, mesurer des grandeurs géométriques',
      'Utiliser le lexique, les unités, les instruments de mesures',
      'Résoudre des problèmes impliquant des grandeurs',
      '(Se) repérer et (se) déplacer en utilisant des repères et des représentations',
      'Reconnaître, nommer, décrire des figures géométriques',
      'Reproduire, représenter, construire des figures géométriques',
    ],
    'Histoire-Géographie': [
      'Se repérer dans le temps',
      'Se repérer dans l\'espace',
      'Raisonner, justifier une démarche et les choix effectués',
      'S\'informer dans le monde du numérique',
      'Analyser et comprendre un document',
      'Pratiquer différents langages en histoire et géographie',
      'Coopérer et mutualiser',
    ],
    'Sciences et Vie de la Terre': [
      'Pratiquer des démarches scientifiques',
      'Concevoir, créer, réaliser',
      'S\'approprier des outils et des méthodes',
      'Pratiquer des langages',
      'Mobiliser des outils numériques',
      'Adopter un comportement éthique et responsable',
      'Se situer dans l\'espace et dans le temps',
    ],
    'Éducation Physique et Sportive': [
      'Développer sa motricité et apprendre à s\'exprimer en utilisant son corps',
      'S\'approprier par la pratique physique et sportive, des méthodes et outils',
      'Partager des règles, assumer des rôles et responsabilités',
      'Apprendre à entretenir sa santé par une activité physique régulière',
      'S\'approprier une culture physique sportive et artistique',
    ],
    'Arts Plastiques': [
      'Expérimenter, produire, créer',
      'Mettre en œuvre un projet artistique',
      'S\'exprimer, analyser sa pratique, celle de ses pairs',
      'Se repérer dans les domaines liés aux arts plastiques',
    ],
  };

  @override
  void initState() {
    super.initState();
    _loadPrimaryStudents();
    
    // Pre-select student if provided
    if (widget.studentId != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        final appProvider = context.read<AppProvider>();
        _selectedStudent = appProvider.getStudent(widget.studentId!);
        setState(() {});
      });
    }
  }

  void _loadPrimaryStudents() {
    final appProvider = context.read<AppProvider>();
    final allStudents = appProvider.activeStudents;
    
    _primaryStudents = allStudents.where((student) {
      if (student.classId == null) return false;
      final schoolClass = appProvider.getClass(student.classId!);
      return schoolClass?.isPrimaryLevel == true;
    }).toList();
  }

  @override
  void dispose() {
    _competencyController.dispose();
    _notesController.dispose();
    _contextController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Évaluer une compétence'),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveCompetencyGrade,
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Enregistrer'),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            _buildInfoCard(),
            const SizedBox(height: 16),
            _buildStudentSelection(),
            const SizedBox(height: 16),
            _buildSubjectSelection(),
            const SizedBox(height: 16),
            _buildCompetencySection(),
            const SizedBox(height: 16),
            _buildLevelSelection(),
            const SizedBox(height: 16),
            _buildEvaluationDetails(),
            const SizedBox(height: 16),
            _buildNotesSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard() {
    return Card(
      color: AppTheme.successGreen.withOpacity(0.05),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Row(
              children: [
                Icon(Icons.stars, color: AppTheme.successGreen, size: 28),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Évaluation par compétences',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.successGreen,
                        ),
                      ),
                      const SizedBox(height: 4),
                      const Text(
                        'Système d\'évaluation adapté à l\'école primaire',
                        style: TextStyle(fontSize: 14),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStudentSelection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Élève à évaluer',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<Student>(
              value: _selectedStudent,
              decoration: const InputDecoration(
                labelText: 'Sélectionner un élève *',
                prefixIcon: Icon(Icons.person),
              ),
              validator: (value) {
                if (value == null) {
                  return 'Veuillez sélectionner un élève';
                }
                return null;
              },
              items: _primaryStudents.map((student) {
                final appProvider = context.read<AppProvider>();
                final schoolClass = appProvider.getClass(student.classId!);
                
                return DropdownMenuItem<Student>(
                  value: student,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(student.fullName),
                      Text(
                        schoolClass?.displayName ?? 'Classe non définie',
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
              onChanged: (Student? newValue) {
                setState(() {
                  _selectedStudent = newValue;
                });
              },
            ),
            if (_primaryStudents.isEmpty)
              Container(
                margin: const EdgeInsets.only(top: 12),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppTheme.warningOrange.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppTheme.warningOrange.withOpacity(0.3)),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info, color: AppTheme.warningOrange, size: 20),
                    const SizedBox(width: 8),
                    const Expanded(
                      child: Text(
                        'Aucun élève de primaire trouvé. Assurez-vous d\'avoir créé des classes primaires et d\'y avoir assigné des élèves.',
                        style: TextStyle(fontSize: 12),
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubjectSelection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Matière',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _selectedSubject,
              decoration: const InputDecoration(
                labelText: 'Sélectionner une matière *',
                prefixIcon: Icon(Icons.subject),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Veuillez sélectionner une matière';
                }
                return null;
              },
              items: _commonCompetencies.keys.map((subject) {
                return DropdownMenuItem<String>(
                  value: subject,
                  child: Text(subject),
                );
              }).toList(),
              onChanged: (String? newValue) {
                setState(() {
                  _selectedSubject = newValue;
                  _competencyController.clear(); // Reset competency when subject changes
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCompetencySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Compétence évaluée',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            if (_selectedSubject != null) ...[
              // Quick selection from common competencies
              const Text(
                'Compétences courantes :',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 4,
                children: _commonCompetencies[_selectedSubject!]!.map((competency) {
                  return ActionChip(
                    label: Text(
                      competency,
                      style: const TextStyle(fontSize: 12),
                    ),
                    onPressed: () {
                      setState(() {
                        _competencyController.text = competency;
                      });
                    },
                  );
                }).toList(),
              ),
              const SizedBox(height: 16),
            ],
            
            TextFormField(
              controller: _competencyController,
              decoration: const InputDecoration(
                labelText: 'Nom de la compétence *',
                prefixIcon: Icon(Icons.star_border),
                hintText: 'Ex: Comprendre un texte lu par l\'enseignant',
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Veuillez saisir le nom de la compétence';
                }
                return null;
              },
              maxLines: 2,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLevelSelection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Niveau d\'acquisition',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            ...CompetencyLevel.values.map((level) {
              final color = _getLevelColor(level);
              final isSelected = _selectedLevel == level;
              
              return Container(
                margin: const EdgeInsets.only(bottom: 12),
                child: InkWell(
                  onTap: () {
                    setState(() {
                      _selectedLevel = level;
                    });
                  },
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: isSelected 
                          ? color.withOpacity(0.1)
                          : Colors.grey.withOpacity(0.05),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isSelected 
                            ? color 
                            : Colors.grey.withOpacity(0.3),
                        width: isSelected ? 2 : 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        Radio<CompetencyLevel>(
                          value: level,
                          groupValue: _selectedLevel,
                          onChanged: (CompetencyLevel? value) {
                            setState(() {
                              _selectedLevel = value;
                            });
                          },
                          activeColor: color,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                _getLevelDisplayName(level),
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: isSelected ? color : null,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                _getLevelDescription(level),
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        ),
                        Icon(
                          _getLevelIcon(level),
                          color: color,
                          size: 24,
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }),
            
            if (_selectedLevel == null)
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppTheme.accentRed.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppTheme.accentRed.withOpacity(0.3)),
                ),
                child: const Text(
                  'Veuillez sélectionner un niveau d\'acquisition',
                  style: TextStyle(
                    color: AppTheme.accentRed,
                    fontSize: 12,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildEvaluationDetails() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Détails de l\'évaluation',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            InkWell(
              onTap: _selectDate,
              child: InputDecorator(
                decoration: const InputDecoration(
                  labelText: 'Date d\'évaluation',
                  prefixIcon: Icon(Icons.calendar_today),
                ),
                child: Text(
                  DateFormat('dd/MM/yyyy').format(_evaluationDate),
                  style: const TextStyle(fontSize: 16),
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            TextFormField(
              controller: _contextController,
              decoration: const InputDecoration(
                labelText: 'Contexte d\'évaluation',
                prefixIcon: Icon(Icons.settings),
                hintText: 'Ex: Évaluation en classe, travail de groupe, projet...',
              ),
              maxLines: 2,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Observations',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'Notes et observations',
                prefixIcon: Icon(Icons.note),
                hintText: 'Commentaires sur la performance de l\'élève, conseils pour progresser...',
              ),
              maxLines: 4,
            ),
          ],
        ),
      ),
    );
  }

  String _getLevelDisplayName(CompetencyLevel level) {
    switch (level) {
      case CompetencyLevel.notAcquired:
        return 'Non acquis';
      case CompetencyLevel.inProgress:
        return 'En cours d\'acquisition';
      case CompetencyLevel.acquired:
        return 'Acquis';
      case CompetencyLevel.exceeded:
        return 'Dépassé';
    }
  }

  String _getLevelDescription(CompetencyLevel level) {
    switch (level) {
      case CompetencyLevel.notAcquired:
        return 'L\'élève n\'a pas encore acquis cette compétence';
      case CompetencyLevel.inProgress:
        return 'L\'élève est en cours d\'acquisition de cette compétence';
      case CompetencyLevel.acquired:
        return 'L\'élève maîtrise cette compétence';
      case CompetencyLevel.exceeded:
        return 'L\'élève dépasse les attentes pour cette compétence';
    }
  }

  Color _getLevelColor(CompetencyLevel level) {
    switch (level) {
      case CompetencyLevel.notAcquired:
        return AppTheme.accentRed;
      case CompetencyLevel.inProgress:
        return AppTheme.warningOrange;
      case CompetencyLevel.acquired:
        return AppTheme.successGreen;
      case CompetencyLevel.exceeded:
        return AppTheme.primaryBlue;
    }
  }

  IconData _getLevelIcon(CompetencyLevel level) {
    switch (level) {
      case CompetencyLevel.notAcquired:
        return Icons.close;
      case CompetencyLevel.inProgress:
        return Icons.hourglass_empty;
      case CompetencyLevel.acquired:
        return Icons.check_circle;
      case CompetencyLevel.exceeded:
        return Icons.star;
    }
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _evaluationDate,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
      locale: const Locale('fr', 'FR'),
    );

    if (date != null) {
      setState(() {
        _evaluationDate = date;
      });
    }
  }

  Future<void> _saveCompetencyGrade() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedLevel == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Veuillez sélectionner un niveau d\'acquisition'),
          backgroundColor: AppTheme.accentRed,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final competencyGrade = CompetencyGrade(
        studentId: _selectedStudent!.id,
        competencyName: _competencyController.text.trim(),
        subject: _selectedSubject!,
        level: _selectedLevel!,
        evaluationDate: _evaluationDate,
        notes: _notesController.text.trim().isEmpty
            ? null
            : _notesController.text.trim(),
        evaluationContext: _contextController.text.trim().isEmpty
            ? null
            : _contextController.text.trim(),
      );

      final appProvider = context.read<AppProvider>();
      await appProvider.addCompetencyGrade(competencyGrade);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Compétence "${competencyGrade.competencyName}" évaluée avec succès'),
            backgroundColor: AppTheme.successGreen,
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de l\'enregistrement: $e'),
            backgroundColor: AppTheme.accentRed,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
