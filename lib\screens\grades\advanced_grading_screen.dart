import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/app_provider.dart';
import '../../models/student.dart';
import '../../models/class.dart';
import '../../utils/app_theme.dart';

class AdvancedGradingScreen extends StatefulWidget {
  final String? classId;
  final String? studentId;

  const AdvancedGradingScreen({
    super.key,
    this.classId,
    this.studentId,
  });

  @override
  State<AdvancedGradingScreen> createState() => _AdvancedGradingScreenState();
}

class _AdvancedGradingScreenState extends State<AdvancedGradingScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _selectedSubject = '';
  final String _selectedGradingType = 'numerical'; // numerical, competency, rubric

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Évaluation avancée'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Notes /20', icon: Icon(Icons.grade)),
            Tab(text: 'Compétences', icon: Icon(Icons.stars)),
            Tab(text: 'Rubrique', icon: Icon(Icons.rule)),
          ],
        ),
      ),
      body: Consumer<AppProvider>(
        builder: (context, appProvider, child) {
          return TabBarView(
            controller: _tabController,
            children: [
              _buildNumericalGrading(appProvider),
              _buildCompetencyGrading(appProvider),
              _buildRubricGrading(appProvider),
            ],
          );
        },
      ),
    );
  }

  Widget _buildNumericalGrading(AppProvider appProvider) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildGradingHeader('Évaluation numérique (/20)'),
          const SizedBox(height: 20),
          
          // Subject Selection
          _buildSubjectSelector(),
          const SizedBox(height: 20),
          
          // Grade Categories
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Catégories d\'évaluation',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  _buildGradeCategory(
                    'Contrôles continus',
                    '40%',
                    AppTheme.primaryBlue,
                    [
                      GradeEntry('Contrôle 1', 15.5, DateTime.now().subtract(const Duration(days: 7))),
                      GradeEntry('Contrôle 2', 12.0, DateTime.now().subtract(const Duration(days: 3))),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildGradeCategory(
                    'Devoirs maison',
                    '30%',
                    AppTheme.successGreen,
                    [
                      GradeEntry('DM1', 18.0, DateTime.now().subtract(const Duration(days: 10))),
                      GradeEntry('DM2', 16.5, DateTime.now().subtract(const Duration(days: 5))),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildGradeCategory(
                    'Participation',
                    '30%',
                    AppTheme.warningOrange,
                    [
                      GradeEntry('Oral', 14.0, DateTime.now().subtract(const Duration(days: 2))),
                    ],
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 20),
          
          // Average Calculation
          _buildAverageCalculation(),
        ],
      ),
    );
  }

  Widget _buildCompetencyGrading(AppProvider appProvider) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildGradingHeader('Évaluation par compétences'),
          const SizedBox(height: 20),
          
          // Competency Areas
          _buildCompetencyArea(
            'Français',
            [
              CompetencyItem('Lire', CompetencyLevel.acquired),
              CompetencyItem('Écrire', CompetencyLevel.inProgress),
              CompetencyItem('Comprendre', CompetencyLevel.acquired),
              CompetencyItem('S\'exprimer oralement', CompetencyLevel.exceeded),
            ],
          ),
          const SizedBox(height: 16),
          _buildCompetencyArea(
            'Mathématiques',
            [
              CompetencyItem('Nombres et calculs', CompetencyLevel.acquired),
              CompetencyItem('Géométrie', CompetencyLevel.inProgress),
              CompetencyItem('Grandeurs et mesures', CompetencyLevel.acquired),
              CompetencyItem('Résolution de problèmes', CompetencyLevel.inProgress),
            ],
          ),
          const SizedBox(height: 16),
          _buildCompetencyArea(
            'Sciences',
            [
              CompetencyItem('Observer', CompetencyLevel.acquired),
              CompetencyItem('Expérimenter', CompetencyLevel.exceeded),
              CompetencyItem('Raisonner', CompetencyLevel.inProgress),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRubricGrading(AppProvider appProvider) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildGradingHeader('Évaluation par rubrique'),
          const SizedBox(height: 20),
          
          // Rubric Selection
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Sélectionner une rubrique',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 12),
                  DropdownButtonFormField<String>(
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      hintText: 'Choisir une rubrique...',
                    ),
                    items: const [
                      DropdownMenuItem(value: 'projet', child: Text('Projet de classe')),
                      DropdownMenuItem(value: 'exposé', child: Text('Exposé oral')),
                      DropdownMenuItem(value: 'rédaction', child: Text('Rédaction')),
                    ],
                    onChanged: (value) {},
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 20),
          
          // Sample Rubric
          _buildRubricTable(),
        ],
      ),
    );
  }

  Widget _buildGradingHeader(String title) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppTheme.primaryGreen, AppTheme.primaryGreen.withOpacity(0.7)],
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Icon(Icons.assessment, color: Colors.white, size: 28),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                Text(
                  'Système d\'évaluation moderne et précis',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.9),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubjectSelector() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Matière',
              style: Theme.of(context).textTheme.titleSmall,
            ),
            const SizedBox(height: 8),
            DropdownButtonFormField<String>(
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                hintText: 'Sélectionner une matière...',
              ),
              value: _selectedSubject.isEmpty ? null : _selectedSubject,
              items: const [
                DropdownMenuItem(value: 'français', child: Text('Français')),
                DropdownMenuItem(value: 'mathématiques', child: Text('Mathématiques')),
                DropdownMenuItem(value: 'histoire', child: Text('Histoire-Géographie')),
                DropdownMenuItem(value: 'sciences', child: Text('Sciences')),
                DropdownMenuItem(value: 'anglais', child: Text('Anglais')),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedSubject = value ?? '';
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGradeCategory(String title, String weight, Color color, List<GradeEntry> grades) {
    double average = grades.isEmpty ? 0 : grades.map((g) => g.grade).reduce((a, b) => a + b) / grades.length;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              width: 4,
              height: 20,
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                title,
                style: const TextStyle(fontWeight: FontWeight.w600),
              ),
            ),
            Chip(
              label: Text(weight),
              backgroundColor: color.withOpacity(0.1),
              labelStyle: TextStyle(color: color, fontSize: 12),
            ),
            const SizedBox(width: 8),
            Text(
              '${average.toStringAsFixed(1)}/20',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: _getGradeColor(average),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        ...grades.map((grade) => _buildGradeItem(grade)),
        TextButton.icon(
          onPressed: () => _addGrade(title),
          icon: const Icon(Icons.add, size: 16),
          label: const Text('Ajouter une note'),
          style: TextButton.styleFrom(foregroundColor: color),
        ),
      ],
    );
  }

  Widget _buildGradeItem(GradeEntry grade) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          const SizedBox(width: 12),
          Expanded(child: Text(grade.name)),
          Text(
            '${grade.grade}/20',
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: _getGradeColor(grade.grade),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            _formatDate(grade.date),
            style: const TextStyle(fontSize: 12, color: Colors.grey),
          ),
          IconButton(
            icon: const Icon(Icons.edit, size: 16),
            onPressed: () => _editGrade(grade),
          ),
        ],
      ),
    );
  }

  Widget _buildCompetencyArea(String subject, List<CompetencyItem> competencies) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              subject,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            ...competencies.map((comp) => _buildCompetencyItem(comp)),
          ],
        ),
      ),
    );
  }

  Widget _buildCompetencyItem(CompetencyItem competency) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Expanded(child: Text(competency.name)),
          _buildCompetencyIndicator(competency.level),
        ],
      ),
    );
  }

  Widget _buildCompetencyIndicator(CompetencyLevel level) {
    Color color;
    String text;
    IconData icon;
    
    switch (level) {
      case CompetencyLevel.notAcquired:
        color = AppTheme.accentRed;
        text = 'Non acquis';
        icon = Icons.close;
        break;
      case CompetencyLevel.inProgress:
        color = AppTheme.warningOrange;
        text = 'En cours';
        icon = Icons.more_horiz;
        break;
      case CompetencyLevel.acquired:
        color = AppTheme.successGreen;
        text = 'Acquis';
        icon = Icons.check;
        break;
      case CompetencyLevel.exceeded:
        color = AppTheme.primaryBlue;
        text = 'Dépassé';
        icon = Icons.star;
        break;
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRubricTable() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Rubrique: Exposé oral',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('Critères')),
                  DataColumn(label: Text('Excellent\n(4 pts)')),
                  DataColumn(label: Text('Satisfaisant\n(3 pts)')),
                  DataColumn(label: Text('À améliorer\n(2 pts)')),
                  DataColumn(label: Text('Insuffisant\n(1 pt)')),
                  DataColumn(label: Text('Score')),
                ],
                rows: [
                  _buildRubricRow('Contenu', 3),
                  _buildRubricRow('Expression orale', 4),
                  _buildRubricRow('Support visuel', 2),
                  _buildRubricRow('Gestion du temps', 3),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Text(
                  'Total: 12/16 (75%)',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.successGreen,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  DataRow _buildRubricRow(String criteria, int score) {
    return DataRow(
      cells: [
        DataCell(Text(criteria)),
        DataCell(Radio(value: 4, groupValue: score, onChanged: (v) {})),
        DataCell(Radio(value: 3, groupValue: score, onChanged: (v) {})),
        DataCell(Radio(value: 2, groupValue: score, onChanged: (v) {})),
        DataCell(Radio(value: 1, groupValue: score, onChanged: (v) {})),
        DataCell(Text('$score')),
      ],
    );
  }

  Widget _buildAverageCalculation() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Calcul de la moyenne',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Column(
                    children: [
                      Text(
                        '14.7',
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppTheme.successGreen,
                        ),
                      ),
                      const Text('Moyenne générale'),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    children: [
                      Text(
                        '15.2',
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppTheme.primaryBlue,
                        ),
                      ),
                      const Text('Moyenne classe'),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getGradeColor(double grade) {
    if (grade >= 18) return AppTheme.excellentGreen;
    if (grade >= 16) return AppTheme.veryGoodBlue;
    if (grade >= 14) return AppTheme.goodPurple;
    if (grade >= 12) return AppTheme.fairOrange;
    if (grade >= 10) return AppTheme.passableYellow;
    if (grade >= 8) return AppTheme.insufficientRed;
    return AppTheme.veryInsufficientDarkRed;
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}';
  }

  void _addGrade(String category) {
    // TODO: Implement add grade dialog
  }

  void _editGrade(GradeEntry grade) {
    // TODO: Implement edit grade dialog
  }
}

class GradeEntry {
  final String name;
  final double grade;
  final DateTime date;

  GradeEntry(this.name, this.grade, this.date);
}

class CompetencyItem {
  final String name;
  final CompetencyLevel level;

  CompetencyItem(this.name, this.level);
}
