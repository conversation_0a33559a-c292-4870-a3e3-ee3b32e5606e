import 'package:flutter/material.dart';
import '../services/database_service.dart';
import '../services/settings_service.dart';
import '../services/backup_service.dart';
import '../services/license_service.dart';
import '../models/student.dart';
import '../models/attendance.dart';
import '../models/behavior_note.dart';
import '../models/grade.dart';
import '../models/rubric.dart';
import '../models/lesson.dart';
import '../models/feedback_template.dart';
import '../models/course.dart';
import '../models/class.dart';
import '../models/app_settings.dart';

class AppProvider extends ChangeNotifier {
  // Settings management
  AppSettings _settings = AppSettings();
  AppSettings get settings => _settings;

  // Theme management
  bool get isDarkMode => _settings.isDarkMode;

  void toggleTheme() {
    final newValue = !_settings.isDarkMode;
    _settings = _settings.copyWith(isDarkMode: newValue);
    SettingsService.updateSetting('isDarkMode', newValue);
    notifyListeners();
  }

  void setTheme(bool isDark) {
    _settings = _settings.copyWith(isDarkMode: isDark);
    SettingsService.updateSetting('isDarkMode', isDark);
    notifyListeners();
  }

  // License management
  bool get isLicensed => LicenseService.isLicensed;
  String get licenseStatus => LicenseService.licenseStatusText;

  // Settings management methods
  Future<void> loadSettings() async {
    _settings = SettingsService.currentSettings;
    notifyListeners();
  }

  Future<void> updateSetting(String key, dynamic value) async {
    await SettingsService.updateSetting(key, value);
    _settings = SettingsService.currentSettings;
    notifyListeners();
  }

  // Check if feature is restricted
  bool isFeatureRestricted(String featureName) {
    return LicenseService.isFeatureRestricted(featureName);
  }

  // Show restriction dialog
  void showRestrictionDialog(BuildContext context, String featureName) {
    LicenseService.showRestrictionDialog(context, featureName);
  }

  // Loading states
  bool _isLoading = false;
  bool get isLoading => _isLoading;

  void setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // Students management
  List<Student> _students = [];
  List<Student> get students => _students;
  List<Student> get activeStudents =>
      _students.where((s) => s.isActive).toList();

  Future<void> loadStudents() async {
    setLoading(true);
    try {
      _students = DatabaseService.allStudents;
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading students: $e');
    } finally {
      setLoading(false);
    }
  }

  Future<void> addStudent(Student student) async {
    try {
      await DatabaseService.saveStudent(student);
      _students.add(student);
      notifyListeners();
    } catch (e) {
      debugPrint('Error adding student: $e');
      rethrow;
    }
  }

  Future<void> updateStudent(Student student) async {
    try {
      await DatabaseService.saveStudent(student);
      final index = _students.indexWhere((s) => s.id == student.id);
      if (index != -1) {
        _students[index] = student;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error updating student: $e');
      rethrow;
    }
  }

  Future<void> deleteStudent(String studentId) async {
    try {
      await DatabaseService.deleteStudent(studentId);
      final index = _students.indexWhere((s) => s.id == studentId);
      if (index != -1) {
        _students[index].isActive = false;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error deleting student: $e');
      rethrow;
    }
  }

  Student? getStudent(String studentId) {
    try {
      return _students.firstWhere((s) => s.id == studentId);
    } catch (e) {
      return null;
    }
  }

  // Attendance management
  final Map<String, DailyAttendance> _dailyAttendanceCache = {};

  Future<DailyAttendance?> getDailyAttendance(DateTime date) async {
    final dateKey =
        '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';

    if (_dailyAttendanceCache.containsKey(dateKey)) {
      return _dailyAttendanceCache[dateKey];
    }

    final dailyAttendance = DatabaseService.getDailyAttendance(date);
    if (dailyAttendance != null) {
      _dailyAttendanceCache[dateKey] = dailyAttendance;
    }

    return dailyAttendance;
  }

  Future<DailyAttendance> getOrCreateDailyAttendance(DateTime date) async {
    var dailyAttendance = await getDailyAttendance(date);

    if (dailyAttendance == null) {
      dailyAttendance = DailyAttendance(date: date);
      await DatabaseService.saveDailyAttendance(dailyAttendance);

      final dateKey =
          '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
      _dailyAttendanceCache[dateKey] = dailyAttendance;
    }

    return dailyAttendance;
  }

  Future<void> updateAttendance(
    String studentId,
    DateTime date,
    AttendanceStatus status, {
    String? notes,
  }) async {
    try {
      final dailyAttendance = await getOrCreateDailyAttendance(date);
      dailyAttendance.updateRecord(studentId, status, notes: notes);
      await DatabaseService.saveDailyAttendance(dailyAttendance);
      notifyListeners();
    } catch (e) {
      debugPrint('Error updating attendance: $e');
      rethrow;
    }
  }

  Future<void> saveDailyAttendance(DailyAttendance dailyAttendance) async {
    try {
      await DatabaseService.saveDailyAttendance(dailyAttendance);
      final dateKey =
          '${dailyAttendance.date.year}-${dailyAttendance.date.month.toString().padLeft(2, '0')}-${dailyAttendance.date.day.toString().padLeft(2, '0')}';
      _dailyAttendanceCache[dateKey] = dailyAttendance;
      notifyListeners();
    } catch (e) {
      debugPrint('Error saving daily attendance: $e');
      rethrow;
    }
  }

  // Calculate overall attendance percentage
  String calculateOverallAttendancePercentage() {
    try {
      final allDailyAttendances = DatabaseService.allDailyAttendances;
      if (allDailyAttendances.isEmpty) {
        return '0%';
      }

      int totalRecords = 0;
      int presentRecords = 0;

      for (final dailyAttendance in allDailyAttendances) {
        totalRecords += dailyAttendance.records.length;
        presentRecords += dailyAttendance.records
            .where((r) => r.status == AttendanceStatus.present)
            .length;
      }

      if (totalRecords == 0) {
        return '0%';
      }

      final percentage = (presentRecords / totalRecords * 100).round();
      return '$percentage%';
    } catch (e) {
      debugPrint('Error calculating attendance percentage: $e');
      return '0%';
    }
  }

  // Behavior notes management
  List<BehaviorNote> _behaviorNotes = [];
  List<BehaviorNote> get behaviorNotes => _behaviorNotes;

  Future<void> loadBehaviorNotes() async {
    try {
      _behaviorNotes = DatabaseService.behaviorNotesBox.values.toList();
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading behavior notes: $e');
    }
  }

  Future<void> addBehaviorNote(BehaviorNote note) async {
    try {
      await DatabaseService.saveBehaviorNote(note);
      _behaviorNotes.add(note);
      notifyListeners();
    } catch (e) {
      debugPrint('Error adding behavior note: $e');
      rethrow;
    }
  }

  List<BehaviorNote> getStudentBehaviorNotes(String studentId) {
    return _behaviorNotes.where((note) => note.studentId == studentId).toList();
  }

  Future<void> updateBehaviorNote(BehaviorNote note) async {
    try {
      await DatabaseService.saveBehaviorNote(note);
      final index = _behaviorNotes.indexWhere((n) => n.id == note.id);
      if (index != -1) {
        _behaviorNotes[index] = note;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error updating behavior note: $e');
      rethrow;
    }
  }

  Future<void> deleteBehaviorNote(String noteId) async {
    try {
      await DatabaseService.deleteBehaviorNote(noteId);
      _behaviorNotes.removeWhere((note) => note.id == noteId);
      notifyListeners();
    } catch (e) {
      debugPrint('Error deleting behavior note: $e');
      rethrow;
    }
  }

  // Classes management
  List<SchoolClass>? _classes;
  List<SchoolClass>? get classes => _classes;
  List<SchoolClass> get activeClasses =>
      _classes?.where((c) => c.isActive).toList() ?? [];

  Future<void> loadClasses() async {
    try {
      _classes = DatabaseService.allClasses;
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading classes: $e');
      _classes = [];
    }
  }

  Future<void> addClass(SchoolClass schoolClass) async {
    try {
      await DatabaseService.saveClass(schoolClass);
      _classes ??= [];
      _classes!.add(schoolClass);
      notifyListeners();
    } catch (e) {
      debugPrint('Error adding class: $e');
      rethrow;
    }
  }

  Future<void> updateClass(SchoolClass schoolClass) async {
    try {
      await DatabaseService.saveClass(schoolClass);
      if (_classes != null) {
        final index = _classes!.indexWhere((c) => c.id == schoolClass.id);
        if (index != -1) {
          _classes![index] = schoolClass;
          notifyListeners();
        }
      }
    } catch (e) {
      debugPrint('Error updating class: $e');
      rethrow;
    }
  }

  SchoolClass? getClass(String classId) {
    if (_classes == null) return null;
    try {
      return _classes!.firstWhere((c) => c.id == classId);
    } catch (e) {
      return null;
    }
  }

  List<Student> getClassStudents(String classId) {
    return _students.where((s) => s.classId == classId && s.isActive).toList();
  }

  Future<void> duplicateClass(String classId) async {
    try {
      final originalClass = getClass(classId);
      if (originalClass == null) {
        throw Exception('Classe introuvable');
      }

      // Create a new class with copied data but new ID and updated info
      final duplicatedClass = SchoolClass(
        name: '${originalClass.name} (Copie)',
        level: originalClass.level,
        gradingSystem: originalClass.gradingSystem,
        description: originalClass.description != null
            ? '${originalClass.description!} (Classe dupliquée)'
            : 'Classe dupliquée',
        teacher: originalClass.teacher,
        maxStudents: originalClass.maxStudents,
        classroom: null, // Reset classroom for new class
        academicYear: originalClass.academicYear,
        subjectIds: originalClass.subjectIds != null
            ? List<String>.from(originalClass.subjectIds!)
            : null,
        notes: originalClass.notes != null
            ? 'Dupliquée de: ${originalClass.displayName}\n\n${originalClass.notes!}'
            : 'Dupliquée de: ${originalClass.displayName}',
      );

      await addClass(duplicatedClass);
    } catch (e) {
      debugPrint('Error duplicating class: $e');
      rethrow;
    }
  }

  // Competency grades management (for primary school classes)
  List<CompetencyGrade>? _competencyGrades;
  List<CompetencyGrade>? get competencyGrades => _competencyGrades;

  Future<void> loadCompetencyGrades() async {
    try {
      _competencyGrades = DatabaseService.allCompetencyGrades;
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading competency grades: $e');
      _competencyGrades = [];
    }
  }

  Future<void> addCompetencyGrade(CompetencyGrade competencyGrade) async {
    try {
      await DatabaseService.saveCompetencyGrade(competencyGrade);
      _competencyGrades ??= [];
      _competencyGrades!.add(competencyGrade);
      notifyListeners();
    } catch (e) {
      debugPrint('Error adding competency grade: $e');
      rethrow;
    }
  }

  List<CompetencyGrade> getStudentCompetencyGrades(String studentId) {
    return _competencyGrades?.where((cg) => cg.studentId == studentId).toList() ?? [];
  }

  List<CompetencyGrade> getStudentCompetencyGradesBySubject(
    String studentId, 
    String subject
  ) {
    return _competencyGrades
        ?.where((cg) => cg.studentId == studentId && cg.subject == subject)
        .toList() ?? [];
  }

  // Grades management
  List<Grade> _grades = [];
  List<Grade> get grades => _grades;

  Future<void> loadGrades() async {
    try {
      _grades = DatabaseService.gradesBox.values.toList();
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading grades: $e');
    }
  }

  Future<void> addGrade(Grade grade) async {
    try {
      await DatabaseService.saveGrade(grade);
      _grades.add(grade);
      notifyListeners();
    } catch (e) {
      debugPrint('Error adding grade: $e');
      rethrow;
    }
  }

  List<Grade> getStudentGrades(String studentId) {
    return _grades.where((grade) => grade.studentId == studentId).toList();
  }

  List<Grade> getStudentGradesBySubject(String studentId, String subject) {
    return _grades
        .where(
          (grade) => grade.studentId == studentId && grade.subject == subject,
        )
        .toList();
  }

  double calculateStudentAverage(String studentId, String subject) {
    final studentGrades = getStudentGradesBySubject(studentId, subject);
    if (studentGrades.isEmpty) return 0.0;

    double totalWeightedGrades = 0.0;
    double totalCoefficients = 0.0;

    for (final grade in studentGrades) {
      totalWeightedGrades += grade.normalizedValue * grade.coefficient;
      totalCoefficients += grade.coefficient;
    }

    return totalCoefficients > 0
        ? totalWeightedGrades / totalCoefficients
        : 0.0;
  }

  Future<void> updateGrade(Grade grade) async {
    try {
      await DatabaseService.saveGrade(grade);
      final index = _grades.indexWhere((g) => g.id == grade.id);
      if (index != -1) {
        _grades[index] = grade;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error updating grade: $e');
      rethrow;
    }
  }

  Future<void> deleteGrade(String gradeId) async {
    try {
      await DatabaseService.deleteGrade(gradeId);
      _grades.removeWhere((grade) => grade.id == gradeId);
      notifyListeners();
    } catch (e) {
      debugPrint('Error deleting grade: $e');
      rethrow;
    }
  }

  // Dashboard data methods
  Map<String, dynamic> getDashboardData() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final tomorrow = today.add(const Duration(days: 1));

    return {
      'totalStudents': _students.where((s) => s.isActive).length,
      'todayLessons': _lessons
          .where(
            (lesson) =>
                lesson.startTime.isAfter(today) &&
                lesson.startTime.isBefore(tomorrow),
          )
          .length,
      'recentGrades': _grades
          .where(
            (grade) =>
                grade.createdAt.isAfter(now.subtract(const Duration(days: 7))),
          )
          .length,
      'recentBehaviorNotes': _behaviorNotes
          .where(
            (note) =>
                note.createdAt.isAfter(now.subtract(const Duration(days: 7))),
          )
          .length,
    };
  }

  List<dynamic> getTodayLessons() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final tomorrow = today.add(const Duration(days: 1));

    return _lessons
        .where(
          (lesson) =>
              lesson.startTime.isAfter(today) &&
              lesson.startTime.isBefore(tomorrow),
        )
        .toList()
      ..sort((a, b) => a.startTime.compareTo(b.startTime));
  }

  // Lessons management
  List<Lesson> _lessons = [];
  List<Lesson> get lessons => _lessons;

  Future<void> loadLessons() async {
    try {
      _lessons = DatabaseService.allLessons;
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading lessons: $e');
    }
  }

  Future<void> addLesson(Lesson lesson) async {
    try {
      await DatabaseService.saveLesson(lesson);
      _lessons.add(lesson);
      notifyListeners();
    } catch (e) {
      debugPrint('Error adding lesson: $e');
      rethrow;
    }
  }

  Future<void> updateLesson(Lesson lesson) async {
    try {
      await DatabaseService.saveLesson(lesson);
      final index = _lessons.indexWhere((l) => l.id == lesson.id);
      if (index != -1) {
        _lessons[index] = lesson;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error updating lesson: $e');
      rethrow;
    }
  }

  List<Lesson> getLessonsForDate(DateTime date) {
    return _lessons
        .where(
          (lesson) =>
              lesson.startTime.year == date.year &&
              lesson.startTime.month == date.month &&
              lesson.startTime.day == date.day,
        )
        .toList();
  }

  // getTodayLessons method moved above

  List<Lesson> getUpcomingLessons() {
    final now = DateTime.now();
    return _lessons.where((lesson) => lesson.startTime.isAfter(now)).toList()
      ..sort((a, b) => a.startTime.compareTo(b.startTime));
  }

  Future<void> deleteLesson(String lessonId) async {
    try {
      await DatabaseService.deleteLesson(lessonId);
      _lessons.removeWhere((lesson) => lesson.id == lessonId);
      notifyListeners();
    } catch (e) {
      debugPrint('Error deleting lesson: $e');
      rethrow;
    }
  }

  Future<void> duplicateLesson(String lessonId) async {
    try {
      final originalLesson = _lessons.firstWhere((l) => l.id == lessonId);

      // Create a new lesson with copied data but new ID and updated times
      final duplicatedLesson = Lesson(
        title: '${originalLesson.title} (Copie)',
        subject: originalLesson.subject,
        description: originalLesson.description,
        startTime: originalLesson.startTime.add(
          const Duration(days: 7),
        ), // Next week
        endTime: originalLesson.endTime.add(const Duration(days: 7)),
        status: LessonStatus.planned, // Reset to planned
        classroom: originalLesson.classroom,
        objectives: originalLesson.objectives != null
            ? List<String>.from(originalLesson.objectives!)
            : null,
        content: originalLesson.content,
        homework: originalLesson.homework,
        notes: originalLesson.notes,
        studentIds: originalLesson.studentIds != null
            ? List<String>.from(originalLesson.studentIds!)
            : null,
        isRecurring: originalLesson.isRecurring,
        recurringPattern: originalLesson.recurringPattern,
      );

      await addLesson(duplicatedLesson);
    } catch (e) {
      debugPrint('Error duplicating lesson: $e');
      rethrow;
    }
  }

  Future<void> startLesson(String lessonId) async {
    try {
      final lesson = _lessons.firstWhere((l) => l.id == lessonId);
      final updatedLesson = Lesson(
        id: lesson.id,
        title: lesson.title,
        subject: lesson.subject,
        description: lesson.description,
        startTime: lesson.startTime,
        endTime: lesson.endTime,
        status: LessonStatus.inProgress,
        classroom: lesson.classroom,
        objectives: lesson.objectives,
        content: lesson.content,
        homework: lesson.homework,
        attachments: lesson.attachments,
        notes: lesson.notes,
        createdAt: lesson.createdAt,
        updatedAt: DateTime.now(),
        studentIds: lesson.studentIds,
        isRecurring: lesson.isRecurring,
        recurringPattern: lesson.recurringPattern,
      );

      await updateLesson(updatedLesson);
    } catch (e) {
      debugPrint('Error starting lesson: $e');
      rethrow;
    }
  }

  Future<void> endLesson(String lessonId) async {
    try {
      final lesson = _lessons.firstWhere((l) => l.id == lessonId);
      final updatedLesson = Lesson(
        id: lesson.id,
        title: lesson.title,
        subject: lesson.subject,
        description: lesson.description,
        startTime: lesson.startTime,
        endTime: lesson.endTime,
        status: LessonStatus.completed,
        classroom: lesson.classroom,
        objectives: lesson.objectives,
        content: lesson.content,
        homework: lesson.homework,
        attachments: lesson.attachments,
        notes: lesson.notes,
        createdAt: lesson.createdAt,
        updatedAt: DateTime.now(),
        studentIds: lesson.studentIds,
        isRecurring: lesson.isRecurring,
        recurringPattern: lesson.recurringPattern,
      );

      await updateLesson(updatedLesson);
    } catch (e) {
      debugPrint('Error ending lesson: $e');
      rethrow;
    }
  }

  // Rubrics management
  List<Rubric> _rubrics = [];
  List<Rubric> get rubrics => _rubrics;

  Future<void> loadRubrics() async {
    try {
      _rubrics = DatabaseService.allRubrics;
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading rubrics: $e');
    }
  }

  Future<void> addRubric(Rubric rubric) async {
    try {
      await DatabaseService.saveRubric(rubric);
      _rubrics.add(rubric);
      notifyListeners();
    } catch (e) {
      debugPrint('Error adding rubric: $e');
      rethrow;
    }
  }

  Future<void> updateRubric(Rubric rubric) async {
    try {
      await DatabaseService.saveRubric(rubric);
      final index = _rubrics.indexWhere((r) => r.id == rubric.id);
      if (index != -1) {
        _rubrics[index] = rubric;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error updating rubric: $e');
      rethrow;
    }
  }

  Future<void> deleteRubric(String rubricId) async {
    try {
      await DatabaseService.deleteRubric(rubricId);
      _rubrics.removeWhere((rubric) => rubric.id == rubricId);
      notifyListeners();
    } catch (e) {
      debugPrint('Error deleting rubric: $e');
      rethrow;
    }
  }

  Rubric? getRubric(String rubricId) {
    try {
      return _rubrics.firstWhere((r) => r.id == rubricId);
    } catch (e) {
      return null;
    }
  }

  List<Rubric> getRubricsBySubject(String subject) {
    return _rubrics.where((r) => r.subject == subject).toList();
  }

  // Feedback templates management
  List<FeedbackTemplate> _feedbackTemplates = [];
  List<FeedbackTemplate> get feedbackTemplates => _feedbackTemplates;

  Future<void> loadFeedbackTemplates() async {
    try {
      _feedbackTemplates = DatabaseService.activeFeedbackTemplates;
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading feedback templates: $e');
    }
  }

  List<FeedbackTemplate> getFeedbackTemplatesByCategory(
    FeedbackCategory category,
  ) {
    return _feedbackTemplates
        .where((template) => template.category == category)
        .toList();
  }

  // Subjects management
  List<Subject> _subjects = [];
  List<Subject> get subjects => _subjects;
  List<Subject> get activeSubjects =>
      _subjects.where((s) => s.isActive).toList();

  Future<void> loadSubjects() async {
    try {
      _subjects = DatabaseService.allSubjects;
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading subjects: $e');
    }
  }

  Future<void> addSubject(Subject subject) async {
    try {
      await DatabaseService.saveSubject(subject);
      _subjects.add(subject);
      notifyListeners();
    } catch (e) {
      debugPrint('Error adding subject: $e');
      rethrow;
    }
  }

  Future<void> updateSubject(Subject subject) async {
    try {
      await DatabaseService.saveSubject(subject);
      final index = _subjects.indexWhere((s) => s.id == subject.id);
      if (index != -1) {
        _subjects[index] = subject;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error updating subject: $e');
      rethrow;
    }
  }

  Future<void> deleteSubject(String subjectId) async {
    try {
      await DatabaseService.deleteSubject(subjectId);
      final index = _subjects.indexWhere((s) => s.id == subjectId);
      if (index != -1) {
        _subjects[index].isActive = false;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error deleting subject: $e');
      rethrow;
    }
  }

  Subject? getSubject(String subjectId) {
    try {
      return _subjects.firstWhere((s) => s.id == subjectId);
    } catch (e) {
      return null;
    }
  }

  List<Subject> getSubjectsByCategory(CourseCategory category) {
    return _subjects
        .where((subject) => subject.category == category && subject.isActive)
        .toList();
  }

  // Courses management
  List<Course> _courses = [];
  List<Course> get courses => _courses;
  List<Course> get activeCourses => _courses.where((c) => c.isActive).toList();

  Future<void> loadCourses() async {
    try {
      _courses = DatabaseService.allCourses;
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading courses: $e');
    }
  }

  Future<void> addCourse(Course course) async {
    try {
      await DatabaseService.saveCourse(course);
      _courses.add(course);
      notifyListeners();
    } catch (e) {
      debugPrint('Error adding course: $e');
      rethrow;
    }
  }

  Future<void> updateCourse(Course course) async {
    try {
      await DatabaseService.saveCourse(course);
      final index = _courses.indexWhere((c) => c.id == course.id);
      if (index != -1) {
        _courses[index] = course;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error updating course: $e');
      rethrow;
    }
  }

  Future<void> deleteCourse(String courseId) async {
    try {
      await DatabaseService.deleteCourse(courseId);
      final index = _courses.indexWhere((c) => c.id == courseId);
      if (index != -1) {
        _courses[index].isActive = false;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error deleting course: $e');
      rethrow;
    }
  }

  Course? getCourse(String courseId) {
    try {
      return _courses.firstWhere((c) => c.id == courseId);
    } catch (e) {
      return null;
    }
  }

  Future<void> duplicateCourse(String courseId) async {
    try {
      final originalCourse = _courses.firstWhere((c) => c.id == courseId);

      // Create a new course with copied data but new ID and updated info
      final duplicatedCourse = Course(
        name: '${originalCourse.name} (Copie)',
        code: '${originalCourse.code}-COPY',
        subjectId: originalCourse.subjectId,
        level: originalCourse.level,
        description: originalCourse.description,
        objectives: originalCourse.objectives
            ?.map(
              (obj) => LearningObjective(
                title: obj.title,
                description: obj.description,
                isCompleted: false, // Reset completion status
                order: obj.order,
              ),
            )
            .toList(),
        prerequisites: originalCourse.prerequisites != null
            ? List<String>.from(originalCourse.prerequisites!)
            : null,
        difficulty: originalCourse.difficulty,
        estimatedHours: originalCourse.estimatedHours,
        academicYear: originalCourse.academicYear,
        notes: originalCourse.notes,
      );

      await addCourse(duplicatedCourse);
    } catch (e) {
      debugPrint('Error duplicating course: $e');
      rethrow;
    }
  }

  List<Course> getCoursesBySubject(String subjectId) {
    return _courses
        .where((course) => course.subjectId == subjectId && course.isActive)
        .toList();
  }

  List<Course> getCoursesByLevel(EducationLevel level) {
    return _courses
        .where((course) => course.level == level && course.isActive)
        .toList();
  }

  // getDashboardData method moved above

  // Initialize all data
  Future<void> initializeApp() async {
    print('AppProvider: Starting initializeApp');
    setLoading(true);
    try {
      print('AppProvider: Initializing services');
      // Initialize services first
      await SettingsService.init();
      await LicenseService.init();

      print('AppProvider: Loading settings');
      // Load settings
      await loadSettings();

      print('AppProvider: Loading all data');
      // Load all data
      await Future.wait([
        loadStudents(),
        loadBehaviorNotes(),
        loadGrades(),
        loadLessons(),
        loadRubrics(),
        loadFeedbackTemplates(),
        loadSubjects(),
        loadCourses(),
        loadClasses(),
        loadCompetencyGrades(),
      ]);

      print('AppProvider: Performing auto-backup');
      // Perform auto-backup if enabled
      BackupService.performAutoBackup();
      print('AppProvider: Initialization complete');
    } catch (e) {
      print('AppProvider: Error initializing app: $e');
      debugPrint('Error initializing app: $e');
    } finally {
      print('AppProvider: Setting loading to false');
      setLoading(false);
    }
  }

  // Clear cache
  void clearCache() {
    _dailyAttendanceCache.clear();
  }
}
