import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/app_provider.dart';
import '../../models/class.dart';
import '../../models/course.dart';
import '../../utils/app_theme.dart';

class AddClassScreen extends StatefulWidget {
  const AddClassScreen({super.key});

  @override
  State<AddClassScreen> createState() => _AddClassScreenState();
}

class _AddClassScreenState extends State<AddClassScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _teacherController = TextEditingController();
  final _classroomController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _notesController = TextEditingController();

  EducationLevel? _selectedLevel;
  GradingSystemType? _selectedGradingSystem;
  int _maxStudents = 30;
  String? _academicYear;
  bool _isLoading = false;

  // Predefined academic years
  final List<String> _academicYears = [
    '2024-2025',
    '2025-2026',
    '2026-2027',
    '2027-2028',
  ];

  @override
  void dispose() {
    _nameController.dispose();
    _teacherController.dispose();
    _classroomController.dispose();
    _descriptionController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Créer une classe'),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveClass,
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Enregistrer'),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            _buildBasicInfoSection(),
            const SizedBox(height: 24),
            _buildEducationLevelSection(),
            const SizedBox(height: 24),
            _buildCapacitySection(),
            const SizedBox(height: 24),
            _buildDetailsSection(),
            const SizedBox(height: 24),
            _buildNotesSection(),
            const SizedBox(height: 32),
            _buildGradingSystemInfo(),
          ],
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Informations de base',
              style: TextStyle(
                fontSize: 20, 
                fontWeight: FontWeight.w700,
                color: Theme.of(context).brightness == Brightness.light 
                    ? const Color(0xFF1A1A1A)
                    : Colors.white,
                letterSpacing: 0.3,
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Nom de la classe *',
                prefixIcon: Icon(Icons.class_),
                hintText: 'Ex: CP-A, 6ème B, Terminale S...',
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Le nom de la classe est obligatoire';
                }
                return null;
              },
              textCapitalization: TextCapitalization.words,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _teacherController,
              decoration: const InputDecoration(
                labelText: 'Enseignant principal',
                prefixIcon: Icon(Icons.person),
                hintText: 'Nom de l\'enseignant principal',
              ),
              textCapitalization: TextCapitalization.words,
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _academicYear,
              decoration: const InputDecoration(
                labelText: 'Année scolaire',
                prefixIcon: Icon(Icons.calendar_today),
              ),
              items: _academicYears.map((year) {
                return DropdownMenuItem(
                  value: year,
                  child: Text(year),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _academicYear = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEducationLevelSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Niveau d\'éducation',
              style: TextStyle(
                fontSize: 20, 
                fontWeight: FontWeight.w700,
                color: Theme.of(context).brightness == Brightness.light 
                    ? const Color(0xFF1A1A1A)
                    : Colors.white,
                letterSpacing: 0.3,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Le système de notation sera automatiquement défini selon le niveau',
              style: TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w500,
                color: Theme.of(context).brightness == Brightness.light 
                    ? const Color(0xFF4A4A4A)
                    : Colors.grey[400],
                fontStyle: FontStyle.italic,
                letterSpacing: 0.2,
              ),
            ),
            const SizedBox(height: 16),
            
            // Primary levels
            _buildLevelCategory(
              'École Primaire (Compétences)',
              [
                EducationLevel.cp,
                EducationLevel.ce1,
                EducationLevel.ce2,
                EducationLevel.cm1,
                EducationLevel.cm2,
              ],
              AppTheme.successGreen,
            ),
            
            const SizedBox(height: 16),
            
            // Secondary levels
            _buildLevelCategory(
              'Collège et Lycée (Notes /20)',
              [
                EducationLevel.sixieme,
                EducationLevel.cinquieme,
                EducationLevel.quatrieme,
                EducationLevel.troisieme,
                EducationLevel.seconde,
                EducationLevel.premiere,
                EducationLevel.terminale,
              ],
              AppTheme.warningOrange,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLevelCategory(String title, List<EducationLevel> levels, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
            Text(
              title,
              style: TextStyle(
                fontSize: 17,
                fontWeight: FontWeight.w700,
                color: Theme.of(context).brightness == Brightness.light 
                    ? color.withOpacity(0.9)
                    : color,
                letterSpacing: 0.3,
              ),
            ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: levels.map((level) {
              final isSelected = _selectedLevel == level;
              return ChoiceChip(
                label: Text(_getLevelDisplayName(level)),
                selected: isSelected,
                onSelected: (selected) {
                  if (selected) {
                    setState(() {
                      _selectedLevel = level;
                      // Auto-set grading system based on level
                      _selectedGradingSystem = _isPrimaryLevel(level)
                          ? GradingSystemType.primary
                          : GradingSystemType.secondary;
                    });
                  }
                },
                selectedColor: color.withOpacity(0.2),
                checkmarkColor: color,
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildCapacitySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Capacité et localisation',
              style: TextStyle(
                fontSize: 20, 
                fontWeight: FontWeight.w700,
                color: Theme.of(context).brightness == Brightness.light 
                    ? const Color(0xFF1A1A1A)
                    : Colors.white,
                letterSpacing: 0.3,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Nombre maximum d\'élèves: $_maxStudents',
                        style: TextStyle(
                          fontSize: 17,
                          fontWeight: FontWeight.w600,
                          color: Theme.of(context).brightness == Brightness.light 
                              ? const Color(0xFF2A2A2A)
                              : Colors.white,
                          letterSpacing: 0.2,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Slider(
                        value: _maxStudents.toDouble(),
                        min: 10,
                        max: 40,
                        divisions: 30,
                        label: _maxStudents.toString(),
                        onChanged: (value) {
                          setState(() {
                            _maxStudents = value.round();
                          });
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _classroomController,
              decoration: const InputDecoration(
                labelText: 'Salle de classe',
                prefixIcon: Icon(Icons.room),
                hintText: 'Ex: Salle 101, Bâtiment A...',
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Description',
              style: TextStyle(
                fontSize: 20, 
                fontWeight: FontWeight.w700,
                color: Theme.of(context).brightness == Brightness.light 
                    ? const Color(0xFF1A1A1A)
                    : Colors.white,
                letterSpacing: 0.3,
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description de la classe',
                prefixIcon: Icon(Icons.description),
                hintText: 'Brève description des objectifs, spécificités...',
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Notes additionnelles',
              style: TextStyle(
                fontSize: 20, 
                fontWeight: FontWeight.w700,
                color: Theme.of(context).brightness == Brightness.light 
                    ? const Color(0xFF1A1A1A)
                    : Colors.white,
                letterSpacing: 0.3,
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'Notes privées',
                prefixIcon: Icon(Icons.note),
                hintText: 'Informations supplémentaires, rappels...',
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGradingSystemInfo() {
    if (_selectedGradingSystem == null) {
      return const SizedBox.shrink();
    }

    final isPrimary = _selectedGradingSystem == GradingSystemType.primary;
    
    return Card(
      color: isPrimary 
          ? AppTheme.successGreen.withOpacity(0.05)
          : AppTheme.warningOrange.withOpacity(0.05),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isPrimary ? Icons.stars : Icons.grade,
                  color: isPrimary ? AppTheme.successGreen : AppTheme.warningOrange,
                ),
                const SizedBox(width: 8),
                Text(
                  'Système de notation',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                    color: Theme.of(context).brightness == Brightness.light 
                        ? (isPrimary ? AppTheme.successGreen.withOpacity(0.8) : AppTheme.warningOrange.withOpacity(0.8))
                        : (isPrimary ? AppTheme.successGreen : AppTheme.warningOrange),
                    letterSpacing: 0.3,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              isPrimary
                  ? 'Évaluation par compétences'
                  : 'Notation traditionnelle sur 20',
              style: TextStyle(
                fontSize: 17,
                fontWeight: FontWeight.w600,
                color: Theme.of(context).brightness == Brightness.light 
                    ? const Color(0xFF2A2A2A)
                    : Colors.white,
                letterSpacing: 0.2,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              isPrimary
                  ? 'Les élèves seront évalués selon 4 niveaux :\n• Non acquis\n• En cours d\'acquisition\n• Acquis\n• Dépassé'
                  : 'Les élèves recevront des notes numériques de 0 à 20 avec des appréciations automatiques selon les seuils du système français.',
              style: TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w500,
                color: Theme.of(context).brightness == Brightness.light 
                    ? const Color(0xFF4A4A4A)
                    : Colors.grey[300],
                letterSpacing: 0.2,
                height: 1.4,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getLevelDisplayName(EducationLevel level) {
    switch (level) {
      case EducationLevel.cp:
        return 'CP';
      case EducationLevel.ce1:
        return 'CE1';
      case EducationLevel.ce2:
        return 'CE2';
      case EducationLevel.cm1:
        return 'CM1';
      case EducationLevel.cm2:
        return 'CM2';
      case EducationLevel.sixieme:
        return '6ème';
      case EducationLevel.cinquieme:
        return '5ème';
      case EducationLevel.quatrieme:
        return '4ème';
      case EducationLevel.troisieme:
        return '3ème';
      case EducationLevel.seconde:
        return '2nde';
      case EducationLevel.premiere:
        return '1ère';
      case EducationLevel.terminale:
        return 'Terminale';
    }
  }

  bool _isPrimaryLevel(EducationLevel level) {
    return [
      EducationLevel.cp,
      EducationLevel.ce1,
      EducationLevel.ce2,
      EducationLevel.cm1,
      EducationLevel.cm2,
    ].contains(level);
  }

  Future<void> _saveClass() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedLevel == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Veuillez sélectionner un niveau d\'éducation'),
          backgroundColor: AppTheme.accentRed,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final schoolClass = SchoolClass(
        name: _nameController.text.trim(),
        level: _selectedLevel!,
        gradingSystem: _selectedGradingSystem!,
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
        teacher: _teacherController.text.trim().isEmpty
            ? null
            : _teacherController.text.trim(),
        maxStudents: _maxStudents,
        classroom: _classroomController.text.trim().isEmpty
            ? null
            : _classroomController.text.trim(),
        academicYear: _academicYear,
        notes: _notesController.text.trim().isEmpty
            ? null
            : _notesController.text.trim(),
      );

      final appProvider = context.read<AppProvider>();
      await appProvider.addClass(schoolClass);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Classe "${schoolClass.displayName}" créée avec succès'),
            backgroundColor: AppTheme.successGreen,
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la création: $e'),
            backgroundColor: AppTheme.accentRed,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
