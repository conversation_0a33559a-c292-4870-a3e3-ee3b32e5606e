import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_provider.dart';
import '../models/class.dart';
import '../models/student.dart';
import '../models/attendance.dart';
import '../models/behavior_note.dart';
import '../utils/app_theme.dart';

class ClassAnalyticsCard extends StatelessWidget {
  final SchoolClass schoolClass;
  final bool isExpanded;

  const ClassAnalyticsCard({
    super.key,
    required this.schoolClass,
    this.isExpanded = false,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, appProvider, child) {
        final classStudents = _getClassStudents(appProvider);
        final analytics = _calculateAnalytics(appProvider, classStudents);

        return Card(
          elevation: 4,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(classStudents.length),
                const SizedBox(height: 16),
                _buildQuickStats(analytics),
                if (isExpanded) ...[
                  const SizedBox(height: 20),
                  const Divider(),
                  const SizedBox(height: 16),
                  _buildDetailedAnalytics(appProvider, classStudents, analytics),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader(int studentCount) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: AppTheme.primaryBlue.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            schoolClass.isPrimaryLevel ? Icons.school : Icons.account_balance,
            color: AppTheme.primaryBlue,
            size: 24,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                schoolClass.displayName,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  Icon(
                    Icons.people,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '$studentCount élèves',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: schoolClass.isPrimaryLevel
                          ? AppTheme.successGreen.withOpacity(0.1)
                          : AppTheme.warningOrange.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      schoolClass.gradingSystemDisplayName,
                      style: TextStyle(
                        fontSize: 12,
                        color: schoolClass.isPrimaryLevel
                            ? AppTheme.successGreen
                            : AppTheme.warningOrange,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildQuickStats(Map<String, dynamic> analytics) {
    return Row(
      children: [
        Expanded(
          child: _buildStatItem(
            'Présence',
            '${analytics['attendanceRate']}%',
            Icons.check_circle,
            _getAttendanceColor(analytics['attendanceRate']),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatItem(
            schoolClass.isPrimaryLevel ? 'Compétences' : 'Moyenne',
            schoolClass.isPrimaryLevel 
                ? '${analytics['competencyRate']}%'
                : '${analytics['averageGrade']}/20',
            schoolClass.isPrimaryLevel ? Icons.stars : Icons.grade,
            _getPerformanceColor(
              schoolClass.isPrimaryLevel 
                  ? analytics['competencyRate'] 
                  : (analytics['averageGrade'] * 5) // Convert to percentage
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatItem(
            'Comportement',
            '${analytics['behaviorScore']}%',
            Icons.psychology,
            _getBehaviorColor(analytics['behaviorScore']),
          ),
        ),
      ],
    );
  }

  Widget _buildStatItem(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildDetailedAnalytics(
    AppProvider appProvider,
    List<Student> students,
    Map<String, dynamic> analytics,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Analyse détaillée',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        
        // Performance breakdown
        _buildPerformanceBreakdown(analytics),
        const SizedBox(height: 16),
        
        // Attendance trends
        _buildAttendanceTrends(analytics),
        const SizedBox(height: 16),
        
        // Student distribution
        _buildStudentDistribution(students, analytics),
        const SizedBox(height: 16),
        
        // Recent activity
        _buildRecentActivity(appProvider, students),
      ],
    );
  }

  Widget _buildPerformanceBreakdown(Map<String, dynamic> analytics) {
    if (schoolClass.isPrimaryLevel) {
      return _buildCompetencyBreakdown(analytics);
    } else {
      return _buildGradeBreakdown(analytics);
    }
  }

  Widget _buildCompetencyBreakdown(Map<String, dynamic> analytics) {
    final competencyData = analytics['competencyBreakdown'] as Map<String, int>;
    
    return Card(
      color: AppTheme.successGreen.withOpacity(0.05),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Répartition des compétences',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildCompetencyBar(
                    'Dépassé',
                    competencyData['exceeded'] ?? 0,
                    AppTheme.successGreen,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildCompetencyBar(
                    'Acquis',
                    competencyData['acquired'] ?? 0,
                    AppTheme.primaryGreen,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildCompetencyBar(
                    'En cours',
                    competencyData['inProgress'] ?? 0,
                    AppTheme.warningOrange,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildCompetencyBar(
                    'Non acquis',
                    competencyData['notAcquired'] ?? 0,
                    AppTheme.accentRed,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCompetencyBar(String label, int count, Color color) {
    return Column(
      children: [
        Container(
          height: 8,
          width: double.infinity,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          count.toString(),
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: color,
            fontSize: 14,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 10,
            color: Colors.grey,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildGradeBreakdown(Map<String, dynamic> analytics) {
    return Card(
      color: AppTheme.warningOrange.withOpacity(0.05),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Répartition des notes',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildGradeRangeItem('16-20', analytics['excellent'] ?? 0, AppTheme.successGreen),
                _buildGradeRangeItem('12-16', analytics['good'] ?? 0, AppTheme.primaryGreen),
                _buildGradeRangeItem('8-12', analytics['average'] ?? 0, AppTheme.warningOrange),
                _buildGradeRangeItem('0-8', analytics['poor'] ?? 0, AppTheme.accentRed),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGradeRangeItem(String range, int count, Color color) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.2),
            shape: BoxShape.circle,
          ),
          child: Text(
            count.toString(),
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          range,
          style: const TextStyle(fontSize: 12),
        ),
      ],
    );
  }

  Widget _buildAttendanceTrends(Map<String, dynamic> analytics) {
    return Card(
      color: AppTheme.primaryBlue.withOpacity(0.05),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.trending_up, size: 16, color: AppTheme.primaryBlue),
                const SizedBox(width: 8),
                const Text(
                  'Tendances de présence',
                  style: TextStyle(fontWeight: FontWeight.w600),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildTrendItem(
                    'Cette semaine',
                    '${analytics['weeklyAttendance']}%',
                    analytics['attendanceTrend'] > 0 ? Icons.arrow_upward : Icons.arrow_downward,
                    analytics['attendanceTrend'] > 0 ? AppTheme.successGreen : AppTheme.accentRed,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildTrendItem(
                    'Ce mois',
                    '${analytics['monthlyAttendance']}%',
                    Icons.calendar_month,
                    AppTheme.primaryBlue,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTrendItem(String period, String value, IconData icon, Color color) {
    return Row(
      children: [
        Icon(icon, size: 16, color: color),
        const SizedBox(width: 8),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              value,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              period,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStudentDistribution(List<Student> students, Map<String, dynamic> analytics) {
    return Card(
      color: AppTheme.neutralGray.withOpacity(0.05),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Distribution des élèves',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildDistributionItem(
                    'Actifs',
                    students.where((s) => s.isActive).length.toString(),
                    Icons.person,
                    AppTheme.successGreen,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildDistributionItem(
                    'Capacité',
                    '${students.length}/${schoolClass.maxStudents}',
                    Icons.group,
                    schoolClass.maxStudents - students.length > 5
                        ? AppTheme.successGreen
                        : AppTheme.warningOrange,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDistributionItem(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 8),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                value,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                label,
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRecentActivity(AppProvider appProvider, List<Student> students) {
    final studentIds = students.map((s) => s.id).toList();
    final recentGrades = appProvider.grades
        .where((g) => studentIds.contains(g.studentId))
        .where((g) => g.createdAt.isAfter(DateTime.now().subtract(const Duration(days: 7))))
        .length;
    
    final recentBehaviorNotes = appProvider.behaviorNotes
        .where((n) => studentIds.contains(n.studentId))
        .where((n) => n.date.isAfter(DateTime.now().subtract(const Duration(days: 7))))
        .length;

    return Card(
      color: AppTheme.secondaryBlue.withOpacity(0.05),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.history, size: 16, color: AppTheme.secondaryBlue),
                const SizedBox(width: 8),
                const Text(
                  'Activité récente (7 derniers jours)',
                  style: TextStyle(fontWeight: FontWeight.w600),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildActivityItem(
                    'Nouvelles notes',
                    recentGrades.toString(),
                    Icons.grade,
                    AppTheme.warningOrange,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildActivityItem(
                    'Notes comportement',
                    recentBehaviorNotes.toString(),
                    Icons.psychology,
                    AppTheme.secondaryBlue,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityItem(String label, String value, IconData icon, Color color) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: color.withOpacity(0.2),
            shape: BoxShape.circle,
          ),
          child: Icon(icon, size: 14, color: color),
        ),
        const SizedBox(width: 8),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              value,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              label,
              style: const TextStyle(
                fontSize: 10,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ],
    );
  }

  List<Student> _getClassStudents(AppProvider appProvider) {
    return appProvider.students
        .where((student) => student.classId == schoolClass.id && student.isActive)
        .toList();
  }

  Map<String, dynamic> _calculateAnalytics(AppProvider appProvider, List<Student> students) {
    if (students.isEmpty) {
      return {
        'attendanceRate': 0,
        'competencyRate': 0,
        'averageGrade': 0.0,
        'behaviorScore': 100,
        'attendanceTrend': 0,
        'weeklyAttendance': 0,
        'monthlyAttendance': 0,
        'competencyBreakdown': <String, int>{},
        'excellent': 0,
        'good': 0,
        'average': 0,
        'poor': 0,
      };
    }

    final studentIds = students.map((s) => s.id).toSet();
    final now = DateTime.now();
    final weekAgo = now.subtract(const Duration(days: 7));
    final monthAgo = now.subtract(const Duration(days: 30));

    // Calculate attendance
    final weeklyAttendanceRecords = <AttendanceRecord>[];
    final monthlyAttendanceRecords = <AttendanceRecord>[];
    
    // This would need to be implemented in your database service
    // For now, returning mock data
    final attendanceRate = 85; // Mock data
    final weeklyAttendance = 88; // Mock data
    final monthlyAttendance = 83; // Mock data

    // Calculate performance based on grading system
    if (schoolClass.isPrimaryLevel) {
      // Competency-based evaluation
      final competencyGrades = appProvider.competencyGrades
          ?.where((cg) => studentIds.contains(cg.studentId))
          .toList() ?? [];
      
      final competencyBreakdown = <String, int>{
        'exceeded': competencyGrades.where((cg) => cg.level == CompetencyLevel.exceeded).length,
        'acquired': competencyGrades.where((cg) => cg.level == CompetencyLevel.acquired).length,
        'inProgress': competencyGrades.where((cg) => cg.level == CompetencyLevel.inProgress).length,
        'notAcquired': competencyGrades.where((cg) => cg.level == CompetencyLevel.notAcquired).length,
      };
      
      final totalCompetencies = competencyGrades.length;
      final acquiredOrBetter = competencyBreakdown['exceeded']! + competencyBreakdown['acquired']!;
      final competencyRate = totalCompetencies > 0 ? (acquiredOrBetter / totalCompetencies * 100).round() : 0;

      return {
        'attendanceRate': attendanceRate,
        'competencyRate': competencyRate,
        'averageGrade': 0.0, // Not applicable for primary
        'behaviorScore': _calculateBehaviorScore(appProvider, studentIds),
        'attendanceTrend': 3, // Mock positive trend
        'weeklyAttendance': weeklyAttendance,
        'monthlyAttendance': monthlyAttendance,
        'competencyBreakdown': competencyBreakdown,
        'excellent': 0,
        'good': 0,
        'average': 0,
        'poor': 0,
      };
    } else {
      // Numerical grading system
      final grades = appProvider.grades
          .where((g) => studentIds.contains(g.studentId))
          .toList();
      
      final averageGrade = grades.isNotEmpty
          ? grades.map((g) => g.normalizedValue).reduce((a, b) => a + b) / grades.length
          : 0.0;
      
      final excellent = grades.where((g) => g.normalizedValue >= 16).length;
      final good = grades.where((g) => g.normalizedValue >= 12 && g.normalizedValue < 16).length;
      final average = grades.where((g) => g.normalizedValue >= 8 && g.normalizedValue < 12).length;
      final poor = grades.where((g) => g.normalizedValue < 8).length;

      return {
        'attendanceRate': attendanceRate,
        'competencyRate': 0, // Not applicable for secondary
        'averageGrade': averageGrade,
        'behaviorScore': _calculateBehaviorScore(appProvider, studentIds),
        'attendanceTrend': 3, // Mock positive trend
        'weeklyAttendance': weeklyAttendance,
        'monthlyAttendance': monthlyAttendance,
        'competencyBreakdown': <String, int>{},
        'excellent': excellent,
        'good': good,
        'average': average,
        'poor': poor,
      };
    }
  }

  int _calculateBehaviorScore(AppProvider appProvider, Set<String> studentIds) {
    final behaviorNotes = appProvider.behaviorNotes
        .where((bn) => studentIds.contains(bn.studentId))
        .where((bn) => bn.date.isAfter(DateTime.now().subtract(const Duration(days: 30))))
        .toList();
    
    if (behaviorNotes.isEmpty) return 100;
    
    final positiveNotes = behaviorNotes.where((bn) => bn.type == BehaviorType.positive).length;
    final negativeNotes = behaviorNotes.where((bn) => bn.type == BehaviorType.negative).length;
    final totalNotes = behaviorNotes.length;
    
    // Calculate behavior score (higher is better)
    if (totalNotes == 0) return 100;
    
    final positiveRatio = positiveNotes / totalNotes;
    final score = (positiveRatio * 100).round();
    
    return score.clamp(0, 100);
  }

  Color _getAttendanceColor(int rate) {
    if (rate >= 90) return AppTheme.successGreen;
    if (rate >= 75) return AppTheme.warningOrange;
    return AppTheme.accentRed;
  }

  Color _getPerformanceColor(double rate) {
    if (rate >= 80) return AppTheme.successGreen;
    if (rate >= 60) return AppTheme.warningOrange;
    return AppTheme.accentRed;
  }

  Color _getBehaviorColor(int score) {
    if (score >= 80) return AppTheme.successGreen;
    if (score >= 60) return AppTheme.warningOrange;
    return AppTheme.accentRed;
  }
}
