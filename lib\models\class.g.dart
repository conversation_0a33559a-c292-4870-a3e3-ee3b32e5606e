// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'class.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class SchoolClassAdapter extends TypeAdapter<SchoolClass> {
  @override
  final int typeId = 32;

  @override
  SchoolClass read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return SchoolClass(
      id: fields[0] as String?,
      name: fields[1] as String,
      level: fields[2] as EducationLevel,
      gradingSystem: fields[3] as GradingSystemType,
      description: fields[4] as String?,
      teacher: fields[5] as String?,
      maxStudents: fields[6] as int,
      classroom: fields[7] as String?,
      academicYear: fields[8] as String?,
      subjectIds: (fields[9] as List?)?.cast<String>(),
      isActive: fields[10] as bool,
      createdAt: fields[11] as DateTime?,
      updatedAt: fields[12] as DateTime?,
      notes: fields[13] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, SchoolClass obj) {
    writer
      ..writeByte(14)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.level)
      ..writeByte(3)
      ..write(obj.gradingSystem)
      ..writeByte(4)
      ..write(obj.description)
      ..writeByte(5)
      ..write(obj.teacher)
      ..writeByte(6)
      ..write(obj.maxStudents)
      ..writeByte(7)
      ..write(obj.classroom)
      ..writeByte(8)
      ..write(obj.academicYear)
      ..writeByte(9)
      ..write(obj.subjectIds)
      ..writeByte(10)
      ..write(obj.isActive)
      ..writeByte(11)
      ..write(obj.createdAt)
      ..writeByte(12)
      ..write(obj.updatedAt)
      ..writeByte(13)
      ..write(obj.notes);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SchoolClassAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class CompetencyGradeAdapter extends TypeAdapter<CompetencyGrade> {
  @override
  final int typeId = 33;

  @override
  CompetencyGrade read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return CompetencyGrade(
      id: fields[0] as String?,
      studentId: fields[1] as String,
      competencyName: fields[2] as String,
      subject: fields[3] as String,
      level: fields[4] as CompetencyLevel,
      evaluationDate: fields[5] as DateTime,
      notes: fields[6] as String?,
      createdAt: fields[7] as DateTime?,
      updatedAt: fields[8] as DateTime?,
      evaluationContext: fields[9] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, CompetencyGrade obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.studentId)
      ..writeByte(2)
      ..write(obj.competencyName)
      ..writeByte(3)
      ..write(obj.subject)
      ..writeByte(4)
      ..write(obj.level)
      ..writeByte(5)
      ..write(obj.evaluationDate)
      ..writeByte(6)
      ..write(obj.notes)
      ..writeByte(7)
      ..write(obj.createdAt)
      ..writeByte(8)
      ..write(obj.updatedAt)
      ..writeByte(9)
      ..write(obj.evaluationContext);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CompetencyGradeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class GradingSystemTypeAdapter extends TypeAdapter<GradingSystemType> {
  @override
  final int typeId = 30;

  @override
  GradingSystemType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return GradingSystemType.primary;
      case 1:
        return GradingSystemType.secondary;
      default:
        return GradingSystemType.primary;
    }
  }

  @override
  void write(BinaryWriter writer, GradingSystemType obj) {
    switch (obj) {
      case GradingSystemType.primary:
        writer.writeByte(0);
        break;
      case GradingSystemType.secondary:
        writer.writeByte(1);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is GradingSystemTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class CompetencyLevelAdapter extends TypeAdapter<CompetencyLevel> {
  @override
  final int typeId = 31;

  @override
  CompetencyLevel read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return CompetencyLevel.notAcquired;
      case 1:
        return CompetencyLevel.inProgress;
      case 2:
        return CompetencyLevel.acquired;
      case 3:
        return CompetencyLevel.exceeded;
      default:
        return CompetencyLevel.notAcquired;
    }
  }

  @override
  void write(BinaryWriter writer, CompetencyLevel obj) {
    switch (obj) {
      case CompetencyLevel.notAcquired:
        writer.writeByte(0);
        break;
      case CompetencyLevel.inProgress:
        writer.writeByte(1);
        break;
      case CompetencyLevel.acquired:
        writer.writeByte(2);
        break;
      case CompetencyLevel.exceeded:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CompetencyLevelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
