import 'package:flutter/material.dart';
import '../screens/students/students_screen.dart';
import '../screens/students/add_student_screen.dart';
import '../screens/students/student_detail_screen.dart';
import '../screens/students/edit_student_screen.dart';
import '../screens/attendance/attendance_screen.dart';
import '../screens/attendance/attendance_history_screen.dart';
import '../screens/attendance/daily_attendance_screen.dart';
import '../screens/behavior/behavior_notes_screen.dart';
import '../screens/behavior/add_behavior_note_screen.dart';
import '../screens/grades/grades_screen.dart';
import '../screens/grades/add_grade_screen.dart';
import '../screens/grades/advanced_grading_screen.dart';
import '../screens/dashboard/enhanced_dashboard_screen.dart';
import '../screens/courses/courses_screen.dart';
import '../screens/courses/subjects_screen.dart';
import '../screens/courses/add_course_screen.dart';
import '../screens/courses/add_subject_screen.dart';
import '../screens/courses/course_detail_screen.dart';
import '../screens/courses/edit_course_screen.dart';
import '../screens/courses/subject_detail_screen.dart';
import '../screens/courses/edit_subject_screen.dart';
import '../screens/classes/classes_screen.dart';
import '../screens/classes/add_class_screen.dart';
import '../screens/classes/class_detail_screen.dart';
import '../screens/classes/edit_class_screen.dart';
import '../screens/classes/assign_students_screen.dart';
import '../screens/grades/add_competency_grade_screen.dart';
import '../screens/grades/rubrics_screen.dart';
import '../screens/lessons/lessons_screen.dart';
import '../screens/lessons/add_lesson_screen.dart';
import '../screens/lessons/lesson_detail_screen.dart';
import '../screens/lessons/timetable_screen.dart';
import '../screens/settings/settings_screen.dart';
import '../screens/settings/backup_screen.dart';
import '../screens/help/user_manual_screen.dart';

class AppRouter {
  // Route names
  static const String home = '/';
  static const String dashboard = '/dashboard';
  static const String students = '/students';
  static const String studentDetail = '/student-detail';
  static const String addStudent = '/add-student';
  static const String editStudent = '/edit-student';
  static const String attendance = '/attendance';
  static const String dailyAttendance = '/daily-attendance';
  static const String attendanceHistory = '/attendance-history';
  static const String behaviorNotes = '/behavior-notes';
  static const String addBehaviorNote = '/add-behavior-note';
  static const String grades = '/grades';
  static const String addGrade = '/add-grade';
  static const String gradeDetail = '/grade-detail';
  static const String rubrics = '/rubrics';
  static const String createRubric = '/create-rubric';
  static const String rubricAssessment = '/rubric-assessment';
  static const String lessons = '/lessons';
  static const String lessonDetail = '/lesson-detail';
  static const String addLesson = '/add-lesson';
  static const String editLesson = '/edit-lesson';
  static const String timetable = '/timetable';
  static const String settings = '/settings';
  static const String backup = '/backup';
  static const String feedbackTemplates = '/feedback-templates';
  static const String userManual = '/user-manual';
  static const String courses = '/courses';
  static const String courseDetail = '/course-detail';
  static const String addCourse = '/add-course';
  static const String editCourse = '/edit-course';
  static const String subjects = '/subjects';
  static const String subjectDetail = '/subject-detail';
  static const String addSubject = '/add-subject';
  static const String editSubject = '/edit-subject';
  static const String classes = '/classes';
  static const String classDetail = '/class-detail';
  static const String addClass = '/classes/add';
  static const String editClass = '/classes/edit';
  static const String assignStudents = '/classes/assign-students';
  static const String addCompetencyGrade = '/competency-grades/add';
  static const String advancedGrading = '/grades/advanced';

  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case home:
      case dashboard:
        return MaterialPageRoute(
          builder: (_) => const EnhancedDashboardScreen(),
          settings: settings,
        );

      case students:
        return MaterialPageRoute(
          builder: (_) => const StudentsScreen(),
          settings: settings,
        );

      case studentDetail:
        final studentId = settings.arguments as String?;
        if (studentId == null) {
          return _errorRoute('Student ID is required');
        }
        return MaterialPageRoute(
          builder: (_) => StudentDetailScreen(studentId: studentId),
          settings: settings,
        );

      case addStudent:
        return MaterialPageRoute(
          builder: (_) => const AddStudentScreen(),
          settings: settings,
        );

      case editStudent:
        final studentId = settings.arguments as String?;
        if (studentId == null) {
          return _errorRoute('Student ID is required');
        }
        return MaterialPageRoute(
          builder: (_) => EditStudentScreen(studentId: studentId),
          settings: settings,
        );

      case attendance:
        return MaterialPageRoute(
          builder: (_) => const AttendanceScreen(),
          settings: settings,
        );

      case dailyAttendance:
        final date = settings.arguments as DateTime?;
        return MaterialPageRoute(
          builder: (_) => DailyAttendanceScreen(date: date),
          settings: settings,
        );

      case attendanceHistory:
        return MaterialPageRoute(
          builder: (_) => const AttendanceHistoryScreen(),
          settings: settings,
        );

      case behaviorNotes:
        return MaterialPageRoute(
          builder: (_) => const BehaviorNotesScreen(),
          settings: settings,
        );

      case addBehaviorNote:
        final args = settings.arguments as Map<String, dynamic>?;
        return MaterialPageRoute(
          builder: (_) => AddBehaviorNoteScreen(
            studentId: args?['studentId'],
            lessonId: args?['lessonId'],
          ),
          settings: settings,
        );

      case grades:
        return MaterialPageRoute(
          builder: (_) => const GradesScreen(),
          settings: settings,
        );

      case addGrade:
        final args = settings.arguments as Map<String, dynamic>?;
        return MaterialPageRoute(
          builder: (_) => AddGradeScreen(
            studentId: args?['studentId'],
            subject: args?['subject'],
          ),
          settings: settings,
        );

      case gradeDetail:
        final gradeId = settings.arguments as String?;
        if (gradeId == null) {
          return _errorRoute('Grade ID is required');
        }
        return MaterialPageRoute(
          builder: (_) => GradeDetailScreen(gradeId: gradeId),
          settings: settings,
        );

      case advancedGrading:
        final args = settings.arguments as Map<String, dynamic>?;
        return MaterialPageRoute(
          builder: (_) => AdvancedGradingScreen(
            classId: args?['classId'],
            studentId: args?['studentId'],
          ),
          settings: settings,
        );

      case rubrics:
        return MaterialPageRoute(
          builder: (_) => const RubricsScreen(),
          settings: settings,
        );

      case createRubric:
        return MaterialPageRoute(
          builder: (_) => const CreateRubricScreen(),
          settings: settings,
        );

      case rubricAssessment:
        final args = settings.arguments as Map<String, dynamic>?;
        if (args?['rubricId'] == null || args?['studentId'] == null) {
          return _errorRoute('Rubric ID and Student ID are required');
        }
        return MaterialPageRoute(
          builder: (_) => RubricAssessmentScreen(
            rubricId: args!['rubricId'],
            studentId: args['studentId'],
            assessmentId: args['assessmentId'],
          ),
          settings: settings,
        );

      case lessons:
        return MaterialPageRoute(
          builder: (_) => const LessonsScreen(),
          settings: settings,
        );

      case lessonDetail:
        final lessonId = settings.arguments as String?;
        if (lessonId == null) {
          return _errorRoute('Lesson ID is required');
        }
        return MaterialPageRoute(
          builder: (_) => LessonDetailScreen(lessonId: lessonId),
          settings: settings,
        );

      case addLesson:
        final date = settings.arguments as DateTime?;
        return MaterialPageRoute(
          builder: (_) => AddLessonScreen(initialDate: date),
          settings: settings,
        );

      case editLesson:
        final lessonId = settings.arguments as String?;
        if (lessonId == null) {
          return _errorRoute('Lesson ID is required');
        }
        return MaterialPageRoute(
          builder: (_) => EditLessonScreen(lessonId: lessonId),
          settings: settings,
        );

      case timetable:
        return MaterialPageRoute(
          builder: (_) => const TimetableScreen(),
          settings: settings,
        );

      case AppRouter.settings:
        return MaterialPageRoute(
          builder: (_) => const SettingsScreen(),
          settings: settings,
        );

      case backup:
        return MaterialPageRoute(
          builder: (_) => const BackupScreen(),
          settings: settings,
        );

      case feedbackTemplates:
        return MaterialPageRoute(
          builder: (_) => const FeedbackTemplatesScreen(),
          settings: settings,
        );

      case userManual:
        return MaterialPageRoute(
          builder: (_) => const UserManualScreen(),
          settings: settings,
        );

      case courses:
        return MaterialPageRoute(
          builder: (_) => const CoursesScreen(),
          settings: settings,
        );

      case addCourse:
        return MaterialPageRoute(
          builder: (_) => const AddCourseScreen(),
          settings: settings,
        );

      case subjects:
        return MaterialPageRoute(
          builder: (_) => const SubjectsScreen(),
          settings: settings,
        );

      case addSubject:
        return MaterialPageRoute(
          builder: (_) => const AddSubjectScreen(),
          settings: settings,
        );

      case courseDetail:
        final courseId = settings.arguments as String?;
        if (courseId == null) {
          return _errorRoute('Course ID is required');
        }
        return MaterialPageRoute(
          builder: (_) => CourseDetailScreen(courseId: courseId),
          settings: settings,
        );

      case editCourse:
        final courseId = settings.arguments as String?;
        if (courseId == null) {
          return _errorRoute('Course ID is required');
        }
        return MaterialPageRoute(
          builder: (_) => EditCourseScreen(courseId: courseId),
          settings: settings,
        );

      case subjectDetail:
        final subjectId = settings.arguments as String?;
        if (subjectId == null) {
          return _errorRoute('Subject ID is required');
        }
        return MaterialPageRoute(
          builder: (_) => SubjectDetailScreen(subjectId: subjectId),
          settings: settings,
        );

      case editSubject:
        final subjectId = settings.arguments as String?;
        if (subjectId == null) {
          return _errorRoute('Subject ID is required');
        }
        return MaterialPageRoute(
          builder: (_) => EditSubjectScreen(subjectId: subjectId),
          settings: settings,
        );

      case classes:
        return MaterialPageRoute(
          builder: (_) => const ClassesScreen(),
          settings: settings,
        );

      case addClass:
        return MaterialPageRoute(
          builder: (_) => const AddClassScreen(),
          settings: settings,
        );

      case classDetail:
        final classId = settings.arguments as String?;
        if (classId == null) {
          return _errorRoute('Class ID is required');
        }
        return MaterialPageRoute(
          builder: (_) => ClassDetailScreen(classId: classId),
          settings: settings,
        );

      case editClass:
        final classId = settings.arguments as String?;
        if (classId == null) {
          return _errorRoute('Class ID is required');
        }
        return MaterialPageRoute(
          builder: (_) => EditClassScreen(classId: classId),
          settings: settings,
        );

      case assignStudents:
        final classId = settings.arguments as String?;
        return MaterialPageRoute(
          builder: (_) => AssignStudentsScreen(targetClassId: classId),
          settings: settings,
        );

      case addCompetencyGrade:
        final args = settings.arguments as Map<String, dynamic>?;
        return MaterialPageRoute(
          builder: (_) => AddCompetencyGradeScreen(
            studentId: args?['studentId'],
            classId: args?['classId'],
          ),
          settings: settings,
        );

      default:
        return _errorRoute('Route not found: ${settings.name}');
    }
  }

  static Route<dynamic> _errorRoute(String message) {
    return MaterialPageRoute(
      builder: (context) => Scaffold(
        appBar: AppBar(title: const Text('Erreur')),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              Text(
                'Erreur de navigation',
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                message,
                style: const TextStyle(fontSize: 16),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () => Navigator.of(
                  context,
                ).pushNamedAndRemoveUntil(dashboard, (route) => false),
                child: const Text('Retour au tableau de bord'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// DashboardScreen is now imported from ../screens/dashboard/dashboard_screen.dart

// StudentsScreen is now imported from ../screens/students/students_screen.dart

// StudentDetailScreen is now imported from ../screens/students/student_detail_screen.dart

// AddStudentScreen is now imported from ../screens/students/add_student_screen.dart

// EditStudentScreen is now imported from ../screens/students/edit_student_screen.dart

// AttendanceScreen is now imported from ../screens/attendance/attendance_screen.dart

// DailyAttendanceScreen is now imported from ../screens/attendance/daily_attendance_screen.dart

// AttendanceHistoryScreen is now imported from ../screens/attendance/attendance_history_screen.dart

// BehaviorNotesScreen is now imported from ../screens/behavior/behavior_notes_screen.dart

// AddBehaviorNoteScreen is now imported from ../screens/behavior/add_behavior_note_screen.dart

// GradesScreen is now imported from ../screens/grades/grades_screen.dart

// AddGradeScreen is now imported from ../screens/grades/add_grade_screen.dart

class GradeDetailScreen extends StatelessWidget {
  final String gradeId;
  const GradeDetailScreen({super.key, required this.gradeId});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(child: Text('Grade Detail: $gradeId - À implémenter')),
    );
  }
}

class CreateRubricScreen extends StatelessWidget {
  const CreateRubricScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(child: Text('Create Rubric - À implémenter')),
    );
  }
}

class RubricAssessmentScreen extends StatelessWidget {
  final String rubricId;
  final String studentId;
  final String? assessmentId;
  const RubricAssessmentScreen({
    super.key,
    required this.rubricId,
    required this.studentId,
    this.assessmentId,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Text('Rubric Assessment: $rubricId, $studentId - À implémenter'),
      ),
    );
  }
}

class EditLessonScreen extends StatelessWidget {
  final String lessonId;
  const EditLessonScreen({super.key, required this.lessonId});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(child: Text('Edit Lesson: $lessonId - À implémenter')),
    );
  }
}

class FeedbackTemplatesScreen extends StatelessWidget {
  const FeedbackTemplatesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(child: Text('Feedback Templates - À implémenter')),
    );
  }
}

class PlaceholderScreen extends StatelessWidget {
  final String title;
  final String message;
  const PlaceholderScreen({
    super.key,
    required this.title,
    required this.message,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(title)),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.construction, size: 64, color: Colors.orange),
            const SizedBox(height: 16),
            Text(
              title,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              message,
              style: const TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
