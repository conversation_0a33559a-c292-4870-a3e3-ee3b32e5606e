import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';
import 'course.dart';

part 'class.g.dart';

// Grading system types
@HiveType(typeId: 30)
enum GradingSystemType {
  @HiveField(0)
  primary, // Elementary school: CP to CM2 (competency-based evaluation)
  @HiveField(1)
  secondary, // Middle/High school: 6ème to Terminale (numerical grades /20)
}

// Competency levels for primary school
@HiveType(typeId: 31)
enum CompetencyLevel {
  @HiveField(0)
  notAcquired, // Non acquis
  @HiveField(1)
  inProgress, // En cours d'acquisition
  @HiveField(2)
  acquired, // Acquis
  @HiveField(3)
  exceeded, // Dépassé
}

// Class model
@HiveType(typeId: 32)
class SchoolClass extends HiveObject {
  @HiveField(0)
  late String id;

  @HiveField(1)
  late String name; // e.g., "CP-A", "6ème B", "Terminale S"

  @HiveField(2)
  late EducationLevel level;

  @HiveField(3)
  late GradingSystemType gradingSystem;

  @HiveField(4)
  String? description;

  @HiveField(5)
  String? teacher; // Main teacher name

  @HiveField(6)
  int maxStudents; // Maximum number of students

  @HiveField(7)
  String? classroom; // Physical classroom/room number

  @HiveField(8)
  String? academicYear; // e.g., "2024-2025"

  @HiveField(9)
  List<String>? subjectIds; // Associated subjects

  @HiveField(10)
  bool isActive;

  @HiveField(11)
  late DateTime createdAt;

  @HiveField(12)
  late DateTime updatedAt;

  @HiveField(13)
  String? notes; // Additional notes about the class

  SchoolClass({
    String? id,
    required this.name,
    required this.level,
    required this.gradingSystem,
    this.description,
    this.teacher,
    this.maxStudents = 30,
    this.classroom,
    this.academicYear,
    this.subjectIds,
    this.isActive = true,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.notes,
  }) {
    this.id = id ?? const Uuid().v4();
    this.createdAt = createdAt ?? DateTime.now();
    this.updatedAt = updatedAt ?? DateTime.now();
    subjectIds ??= [];
  }

  // Get grading system display name
  String get gradingSystemDisplayName {
    switch (gradingSystem) {
      case GradingSystemType.primary:
        return 'Évaluation par compétences';
      case GradingSystemType.secondary:
        return 'Notation sur 20';
    }
  }

  // Get education level display name
  String get levelDisplayName {
    switch (level) {
      case EducationLevel.cp:
        return 'CP';
      case EducationLevel.ce1:
        return 'CE1';
      case EducationLevel.ce2:
        return 'CE2';
      case EducationLevel.cm1:
        return 'CM1';
      case EducationLevel.cm2:
        return 'CM2';
      case EducationLevel.sixieme:
        return '6ème';
      case EducationLevel.cinquieme:
        return '5ème';
      case EducationLevel.quatrieme:
        return '4ème';
      case EducationLevel.troisieme:
        return '3ème';
      case EducationLevel.seconde:
        return '2nde';
      case EducationLevel.premiere:
        return '1ère';
      case EducationLevel.terminale:
        return 'Terminale';
    }
  }

  // Determine if this is a primary school level
  bool get isPrimaryLevel {
    return [
      EducationLevel.cp,
      EducationLevel.ce1,
      EducationLevel.ce2,
      EducationLevel.cm1,
      EducationLevel.cm2,
    ].contains(level);
  }

  // Determine if this is a secondary school level
  bool get isSecondaryLevel {
    return [
      EducationLevel.sixieme,
      EducationLevel.cinquieme,
      EducationLevel.quatrieme,
      EducationLevel.troisieme,
      EducationLevel.seconde,
      EducationLevel.premiere,
      EducationLevel.terminale,
    ].contains(level);
  }

  String get displayName => '$name ($levelDisplayName)';

  void updateTimestamp() {
    updatedAt = DateTime.now();
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'level': level.name,
    'gradingSystem': gradingSystem.name,
    'description': description,
    'teacher': teacher,
    'maxStudents': maxStudents,
    'classroom': classroom,
    'academicYear': academicYear,
    'subjectIds': subjectIds,
    'isActive': isActive,
    'createdAt': createdAt.toIso8601String(),
    'updatedAt': updatedAt.toIso8601String(),
    'notes': notes,
  };

  factory SchoolClass.fromJson(Map<String, dynamic> json) => SchoolClass(
    id: json['id'],
    name: json['name'],
    level: EducationLevel.values.firstWhere(
      (e) => e.name == json['level'],
      orElse: () => EducationLevel.cp,
    ),
    gradingSystem: GradingSystemType.values.firstWhere(
      (e) => e.name == json['gradingSystem'],
      orElse: () => GradingSystemType.primary,
    ),
    description: json['description'],
    teacher: json['teacher'],
    maxStudents: json['maxStudents'] ?? 30,
    classroom: json['classroom'],
    academicYear: json['academicYear'],
    subjectIds: json['subjectIds'] != null
        ? List<String>.from(json['subjectIds'])
        : null,
    isActive: json['isActive'] ?? true,
    createdAt: json['createdAt'] != null 
        ? DateTime.parse(json['createdAt']) 
        : null,
    updatedAt: json['updatedAt'] != null 
        ? DateTime.parse(json['updatedAt']) 
        : null,
    notes: json['notes'],
  );

  SchoolClass copyWith({
    String? name,
    EducationLevel? level,
    GradingSystemType? gradingSystem,
    String? description,
    String? teacher,
    int? maxStudents,
    String? classroom,
    String? academicYear,
    List<String>? subjectIds,
    bool? isActive,
    String? notes,
  }) {
    return SchoolClass(
      id: id,
      name: name ?? this.name,
      level: level ?? this.level,
      gradingSystem: gradingSystem ?? this.gradingSystem,
      description: description ?? this.description,
      teacher: teacher ?? this.teacher,
      maxStudents: maxStudents ?? this.maxStudents,
      classroom: classroom ?? this.classroom,
      academicYear: academicYear ?? this.academicYear,
      subjectIds: subjectIds ?? this.subjectIds,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
      notes: notes ?? this.notes,
    );
  }
}

// Competency-based grade for primary school
@HiveType(typeId: 33)
class CompetencyGrade extends HiveObject {
  @HiveField(0)
  late String id;

  @HiveField(1)
  late String studentId;

  @HiveField(2)
  late String competencyName; // e.g., "Comprendre un texte"

  @HiveField(3)
  late String subject; // Subject area

  @HiveField(4)
  late CompetencyLevel level;

  @HiveField(5)
  late DateTime evaluationDate;

  @HiveField(6)
  String? notes; // Teacher observations

  @HiveField(7)
  late DateTime createdAt;

  @HiveField(8)
  late DateTime updatedAt;

  @HiveField(9)
  String? evaluationContext; // Context of evaluation

  CompetencyGrade({
    String? id,
    required this.studentId,
    required this.competencyName,
    required this.subject,
    required this.level,
    required this.evaluationDate,
    this.notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.evaluationContext,
  }) {
    this.id = id ?? const Uuid().v4();
    this.createdAt = createdAt ?? DateTime.now();
    this.updatedAt = updatedAt ?? DateTime.now();
  }

  String get levelDisplayName {
    switch (level) {
      case CompetencyLevel.notAcquired:
        return 'Non acquis';
      case CompetencyLevel.inProgress:
        return 'En cours d\'acquisition';
      case CompetencyLevel.acquired:
        return 'Acquis';
      case CompetencyLevel.exceeded:
        return 'Dépassé';
    }
  }

  void updateTimestamp() {
    updatedAt = DateTime.now();
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'studentId': studentId,
    'competencyName': competencyName,
    'subject': subject,
    'level': level.name,
    'evaluationDate': evaluationDate.toIso8601String(),
    'notes': notes,
    'createdAt': createdAt.toIso8601String(),
    'updatedAt': updatedAt.toIso8601String(),
    'evaluationContext': evaluationContext,
  };

  factory CompetencyGrade.fromJson(Map<String, dynamic> json) => CompetencyGrade(
    id: json['id'],
    studentId: json['studentId'],
    competencyName: json['competencyName'],
    subject: json['subject'],
    level: CompetencyLevel.values.firstWhere(
      (e) => e.name == json['level'],
      orElse: () => CompetencyLevel.inProgress,
    ),
    evaluationDate: DateTime.parse(json['evaluationDate']),
    notes: json['notes'],
    createdAt: json['createdAt'] != null 
        ? DateTime.parse(json['createdAt']) 
        : null,
    updatedAt: json['updatedAt'] != null 
        ? DateTime.parse(json['updatedAt']) 
        : null,
    evaluationContext: json['evaluationContext'],
  );
}
