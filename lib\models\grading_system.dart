// Grading System Types for different education levels

import 'class.dart';

enum GradingSystem {
  competencyBased,  // For primary education - based on competencies
  numeric,          // For secondary education - numeric grades
}

extension GradingSystemExtension on GradingSystem {
  String get displayName {
    switch (this) {
      case GradingSystem.competencyBased:
        return 'Évaluation par compétences';
      case GradingSystem.numeric:
        return 'Notes numériques (/20)';
    }
  }

  GradingSystemType get type {
    switch (this) {
      case GradingSystem.competencyBased:
        return GradingSystemType.primary;
      case GradingSystem.numeric:
        return GradingSystemType.secondary;
    }
  }
}

extension GradingSystemTypeExtension on GradingSystemType {
  String get displayName {
    switch (this) {
      case GradingSystemType.primary:
        return 'Primaire';
      case GradingSystemType.secondary:
        return 'Secondaire';
    }
  }

  GradingSystem get defaultGradingSystem {
    switch (this) {
      case GradingSystemType.primary:
        return GradingSystem.competencyBased;
      case GradingSystemType.secondary:
        return GradingSystem.numeric;
    }
  }
}
