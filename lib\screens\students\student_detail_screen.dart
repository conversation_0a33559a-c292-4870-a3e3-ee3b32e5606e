import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:io';
import '../../providers/app_provider.dart';
import '../../models/student.dart';
import '../../models/behavior_note.dart';
import '../../models/grade.dart';
import '../../utils/app_router.dart';
import '../../utils/app_theme.dart';
import '../../utils/whatsapp_utils.dart';

class StudentDetailScreen extends StatefulWidget {
  final String studentId;

  const StudentDetailScreen({super.key, required this.studentId});

  @override
  State<StudentDetailScreen> createState() => _StudentDetailScreenState();
}

class _StudentDetailScreenState extends State<StudentDetailScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, appProvider, child) {
        final student = appProvider.getStudent(widget.studentId);

        if (student == null) {
          return Scaffold(
            appBar: AppBar(title: const Text('Élève introuvable')),
            body: const Center(
              child: Text('Cet élève n\'existe pas ou a été supprimé.'),
            ),
          );
        }

        return Scaffold(
          appBar: AppBar(
            title: Text(student.fullName),
            actions: [
              IconButton(
                icon: const Icon(Icons.edit),
                onPressed: () {
                  Navigator.pushNamed(
                    context,
                    AppRouter.editStudent,
                    arguments: student.id,
                  );
                },
                tooltip: 'Modifier',
              ),
              PopupMenuButton<String>(
                onSelected: (value) {
                  switch (value) {
                    case 'delete':
                      _showDeleteConfirmation(context, student, appProvider);
                      break;
                  }
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, color: Colors.red),
                        SizedBox(width: 8),
                        Text('Supprimer', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ],
              ),
            ],
            bottom: TabBar(
              controller: _tabController,
              tabs: const [
                Tab(icon: Icon(Icons.person), text: 'Profil'),
                Tab(icon: Icon(Icons.check_circle), text: 'Présences'),
                Tab(icon: Icon(Icons.grade), text: 'Notes'),
                Tab(icon: Icon(Icons.note), text: 'Comportement'),
              ],
            ),
          ),
          body: TabBarView(
            controller: _tabController,
            children: [
              _buildProfileTab(student),
              _buildAttendanceTab(student, appProvider),
              _buildGradesTab(student, appProvider),
              _buildBehaviorTab(student, appProvider),
            ],
          ),
          floatingActionButton: _buildFloatingActionButton(student),
        );
      },
    );
  }

  Widget _buildProfileTab(Student student) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          _buildProfileHeader(student),
          const SizedBox(height: 24),
          _buildPersonalInfo(student),
          const SizedBox(height: 16),
          _buildContactInfo(student),
          if (student.notes != null && student.notes!.isNotEmpty) ...[
            const SizedBox(height: 16),
            _buildNotesCard(student),
          ],
        ],
      ),
    );
  }

  Widget _buildProfileHeader(Student student) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          children: [
            CircleAvatar(
              radius: 60,
              backgroundColor: AppTheme.primaryBlue.withValues(alpha: 0.1),
              backgroundImage:
                  student.photoPath != null &&
                      File(student.photoPath!).existsSync()
                  ? FileImage(File(student.photoPath!))
                  : null,
              child:
                  student.photoPath == null ||
                      !File(student.photoPath!).existsSync()
                  ? Text(
                      student.initials,
                      style: const TextStyle(
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.primaryBlue,
                      ),
                    )
                  : null,
            ),
            const SizedBox(height: 16),
            Text(
              student.fullName,
              style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            if (student.age != null)
              Text(
                '${student.age} ans',
                style: TextStyle(fontSize: 16, color: Colors.grey[600]),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPersonalInfo(Student student) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Informations personnelles',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildInfoRow(Icons.person, 'Prénom', student.firstName),
            _buildInfoRow(Icons.person_outline, 'Nom', student.lastName),
            if (student.dateOfBirth != null)
              _buildInfoRow(
                Icons.cake,
                'Date de naissance',
                '${student.dateOfBirth!.day}/${student.dateOfBirth!.month}/${student.dateOfBirth!.year}',
              ),
            _buildInfoRow(
              Icons.access_time,
              'Ajouté le',
              '${student.createdAt.day}/${student.createdAt.month}/${student.createdAt.year}',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactInfo(Student student) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Contact des parents',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            if (student.parentEmail != null)
              _buildInfoRow(Icons.email, 'Email', student.parentEmail!)
            else
              _buildInfoRow(Icons.email_outlined, 'Email', 'Non renseigné'),
            if (student.parentPhone != null)
              _buildInfoRow(Icons.phone, 'Téléphone', student.parentPhone!)
            else
              _buildInfoRow(Icons.phone_outlined, 'Téléphone', 'Non renseigné'),
            if (student.parentWhatsApp != null)
              _buildWhatsAppInfoRow(student.parentWhatsApp!)
            else
              _buildInfoRow(Icons.chat_outlined, 'WhatsApp', 'Non renseigné'),
          ],
        ),
      ),
    );
  }

  Widget _buildNotesCard(Student student) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Notes',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(student.notes!, style: const TextStyle(fontSize: 16)),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 12),
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(value, style: const TextStyle(fontSize: 16)),
          ),
        ],
      ),
    );
  }

  Widget _buildWhatsAppInfoRow(String whatsAppNumber) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Icon(Icons.chat, size: 20, color: Colors.green[600]),
          const SizedBox(width: 12),
          Expanded(
            flex: 2,
            child: Text(
              'WhatsApp',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              WhatsAppUtils.formatPhoneForDisplay(whatsAppNumber),
              style: const TextStyle(fontSize: 16),
            ),
          ),
          Expanded(
            flex: 1,
            child: Align(
              alignment: Alignment.centerRight,
              child: Material(
                borderRadius: BorderRadius.circular(20),
                color: Colors.green,
                child: InkWell(
                  borderRadius: BorderRadius.circular(20),
                  onTap: () => _openWhatsApp(whatsAppNumber),
                  child: Container(
                    padding: const EdgeInsets.all(8.0),
                    child: const Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.chat,
                          color: Colors.white,
                          size: 16,
                        ),
                        SizedBox(width: 4),
                        Text(
                          'Ouvrir',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _openWhatsApp(String phoneNumber) async {
    final success = await WhatsAppUtils.openWhatsApp(
      phoneNumber,
      message: 'Bonjour, je vous contacte concernant votre enfant.',
    );

    if (!success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
            'Impossible d\'ouvrir WhatsApp. Vérifiez que l\'application est installée et que le numéro est valide.',
          ),
          backgroundColor: AppTheme.warningOrange,
        ),
      );
    }
  }

  Widget _buildAttendanceTab(Student student, AppProvider appProvider) {
    return const Center(child: Text('Onglet Présences - À implémenter'));
  }

  Widget _buildGradesTab(Student student, AppProvider appProvider) {
    final studentGrades = appProvider.getStudentGrades(student.id);

    if (studentGrades.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.grade_outlined, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'Aucune note',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Ajoutez des notes pour suivre les résultats de ${student.firstName}',
              style: TextStyle(fontSize: 14, color: Colors.grey[500]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    // Group grades by subject
    final gradesBySubject = <String, List<Grade>>{};
    for (final grade in studentGrades) {
      gradesBySubject.putIfAbsent(grade.subject, () => []).add(grade);
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16.0),
      itemCount: gradesBySubject.length,
      itemBuilder: (context, index) {
        final subject = gradesBySubject.keys.elementAt(index);
        final grades = gradesBySubject[subject]!;

        // Sort grades by date (most recent first)
        grades.sort((a, b) => b.date.compareTo(a.date));

        // Calculate subject average
        final average = appProvider.calculateStudentAverage(
          student.id,
          subject,
        );

        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          child: ExpansionTile(
            leading: CircleAvatar(
              backgroundColor: AppTheme.getGradeColor(
                average,
              ).withValues(alpha: 0.1),
              child: Icon(
                Icons.subject,
                color: AppTheme.getGradeColor(average),
                size: 20,
              ),
            ),
            title: Text(
              subject,
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
            subtitle: Text(
              'Moyenne: ${average.toStringAsFixed(1)}/20 - ${_getGradeAppreciation(average)}',
              style: TextStyle(
                color: AppTheme.getGradeColor(average),
                fontWeight: FontWeight.w500,
              ),
            ),
            children: grades.map((grade) {
              return ListTile(
                leading: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: AppTheme.getGradeColor(
                      grade.normalizedValue,
                    ).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: AppTheme.getGradeColor(grade.normalizedValue),
                    ),
                  ),
                  child: Text(
                    grade.displayValue,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.getGradeColor(grade.normalizedValue),
                    ),
                  ),
                ),
                title: Text(grade.title),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${grade.date.day}/${grade.date.month}/${grade.date.year} - ${grade.typeDisplayName}',
                      style: const TextStyle(fontSize: 12),
                    ),
                    if (grade.feedback != null)
                      Text(
                        grade.feedback!,
                        style: const TextStyle(
                          fontSize: 12,
                          fontStyle: FontStyle.italic,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                  ],
                ),
                trailing: grade.coefficient != 1.0
                    ? Chip(
                        label: Text(
                          'x${grade.coefficient}',
                          style: const TextStyle(fontSize: 10),
                        ),
                        backgroundColor: Colors.grey[200],
                      )
                    : null,
                onTap: () {
                  Navigator.pushNamed(
                    context,
                    AppRouter.addGrade,
                    arguments: {
                      'gradeId': grade.id,
                      'studentId': student.id,
                      'subject': grade.subject,
                    },
                  );
                },
              );
            }).toList(),
          ),
        );
      },
    );
  }

  Widget _buildBehaviorTab(Student student, AppProvider appProvider) {
    final behaviorNotes = appProvider.getStudentBehaviorNotes(student.id);

    if (behaviorNotes.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.note_outlined, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'Aucune note de comportement',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Ajoutez des notes pour suivre le comportement de ${student.firstName}',
              style: TextStyle(fontSize: 14, color: Colors.grey[500]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    // Sort notes by date (most recent first)
    behaviorNotes.sort((a, b) => b.date.compareTo(a.date));

    return ListView.builder(
      padding: const EdgeInsets.all(16.0),
      itemCount: behaviorNotes.length,
      itemBuilder: (context, index) {
        final note = behaviorNotes[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: _getBehaviorColor(
                note.type,
              ).withValues(alpha: 0.1),
              child: Icon(
                _getBehaviorIcon(note.type),
                color: _getBehaviorColor(note.type),
                size: 20,
              ),
            ),
            title: Text(
              note.summary.isNotEmpty ? note.summary : note.typeDisplayName,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${note.date.day}/${note.date.month}/${note.date.year} à ${note.date.hour.toString().padLeft(2, '0')}:${note.date.minute.toString().padLeft(2, '0')}',
                  style: const TextStyle(fontSize: 12),
                ),
                if (note.subject != null)
                  Text(
                    'Matière: ${note.subject}',
                    style: const TextStyle(fontSize: 12),
                  ),
              ],
            ),
            trailing: note.isImportant
                ? Icon(
                    Icons.priority_high,
                    color: AppTheme.warningOrange,
                    size: 20,
                  )
                : null,
            onTap: () {
              Navigator.pushNamed(
                context,
                AppRouter.addBehaviorNote,
                arguments: {'behaviorNoteId': note.id, 'studentId': student.id},
              );
            },
          ),
        );
      },
    );
  }

  Color _getBehaviorColor(BehaviorType type) {
    switch (type) {
      case BehaviorType.positive:
        return AppTheme.successGreen;
      case BehaviorType.negative:
        return AppTheme.accentRed;
      case BehaviorType.neutral:
        return AppTheme.neutralGray;
    }
  }

  IconData _getBehaviorIcon(BehaviorType type) {
    switch (type) {
      case BehaviorType.positive:
        return Icons.thumb_up;
      case BehaviorType.negative:
        return Icons.thumb_down;
      case BehaviorType.neutral:
        return Icons.remove;
    }
  }

  String _getGradeAppreciation(double grade) {
    if (grade >= 18) return 'Excellent';
    if (grade >= 16) return 'Très bien';
    if (grade >= 14) return 'Bien';
    if (grade >= 12) return 'Assez bien';
    if (grade >= 10) return 'Passable';
    if (grade >= 8) return 'Insuffisant';
    return 'Très insuffisant';
  }

  Widget? _buildFloatingActionButton(Student student) {
    switch (_tabController.index) {
      case 1: // Attendance tab
        return FloatingActionButton(
          onPressed: () {
            // Navigate to attendance marking
          },
          tooltip: 'Marquer présence',
          child: const Icon(Icons.check),
        );
      case 2: // Grades tab
        return FloatingActionButton(
          onPressed: () {
            Navigator.pushNamed(
              context,
              AppRouter.addGrade,
              arguments: {'studentId': student.id},
            );
          },
          tooltip: 'Ajouter une note',
          child: const Icon(Icons.add),
        );
      case 3: // Behavior tab
        return FloatingActionButton(
          onPressed: () {
            Navigator.pushNamed(
              context,
              AppRouter.addBehaviorNote,
              arguments: {'studentId': student.id},
            );
          },
          tooltip: 'Ajouter une note de comportement',
          child: const Icon(Icons.note_add),
        );
      default:
        return null;
    }
  }

  void _showDeleteConfirmation(
    BuildContext context,
    Student student,
    AppProvider appProvider,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Supprimer l\'élève'),
        content: Text(
          'Êtes-vous sûr de vouloir supprimer ${student.fullName} ? '
          'Cette action ne peut pas être annulée et supprimera également '
          'toutes les données associées (notes, présences, etc.).',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              try {
                await appProvider.deleteStudent(student.id);
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('${student.fullName} a été supprimé'),
                      backgroundColor: AppTheme.successGreen,
                    ),
                  );
                  Navigator.pop(context);
                }
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Erreur lors de la suppression: $e'),
                      backgroundColor: AppTheme.accentRed,
                    ),
                  );
                }
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Supprimer'),
          ),
        ],
      ),
    );
  }
}
