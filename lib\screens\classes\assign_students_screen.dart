import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/app_provider.dart';
import '../../models/class.dart';
import '../../models/student.dart';
import '../../models/course.dart';
import '../../utils/app_theme.dart';

class AssignStudentsScreen extends StatefulWidget {
  final String? targetClassId;
  
  const AssignStudentsScreen({super.key, this.targetClassId});

  @override
  State<AssignStudentsScreen> createState() => _AssignStudentsScreenState();
}

class _AssignStudentsScreenState extends State<AssignStudentsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _searchQuery = '';
  EducationLevel? _selectedLevel;
  String? _selectedTargetClassId;
  final Map<String, bool> _selectedStudents = {};

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _selectedTargetClassId = widget.targetClassId;
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AppProvider>().loadClasses();
      context.read<AppProvider>().loadStudents();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Gestion des membres de classe'),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showSearchDialog,
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Non assignés', icon: Icon(Icons.person_outline)),
            Tab(text: 'Assignations', icon: Icon(Icons.group)),
            Tab(text: 'Gestion par classe', icon: Icon(Icons.class_)),
          ],
        ),
      ),
      body: Consumer<AppProvider>(
        builder: (context, appProvider, child) {
          if (appProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          return TabBarView(
            controller: _tabController,
            children: [
              _buildUnassignedStudentsTab(appProvider),
              _buildAssignmentManagementTab(appProvider),
              _buildClassManagementTab(appProvider),
            ],
          );
        },
      ),
      bottomNavigationBar: _buildBottomActionBar(),
    );
  }

  Widget _buildUnassignedStudentsTab(AppProvider appProvider) {
    final students = _getFilteredUnassignedStudents(appProvider);

    if (students.isEmpty) {
      return _buildEmptyState(
        icon: Icons.people_outline,
        title: 'Aucun élève non assigné',
        subtitle: 'Tous les élèves sont assignés à des classes ou créez de nouveaux élèves',
      );
    }

    return Column(
      children: [
        // Header with actions
        Container(
          padding: const EdgeInsets.all(16),
          color: Colors.grey[50],
          child: Row(
            children: [
              Text(
                '${students.length} élève${students.length > 1 ? 's' : ''} non assigné${students.length > 1 ? 's' : ''}',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              if (_selectedStudents.values.any((selected) => selected))
                Chip(
                  label: Text('${_selectedStudents.values.where((s) => s).length} sélectionné(s)'),
                  backgroundColor: AppTheme.primaryBlue.withOpacity(0.1),
                ),
            ],
          ),
        ),
        
        // Students List
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: students.length,
            itemBuilder: (context, index) {
              final student = students[index];
              final isSelected = _selectedStudents[student.id] ?? false;
              
              return Card(
                margin: const EdgeInsets.only(bottom: 8),
                elevation: isSelected ? 4 : 1,
                color: isSelected ? AppTheme.primaryBlue.withOpacity(0.05) : null,
                child: ListTile(
                  leading: Stack(
                    children: [
                      CircleAvatar(
                        backgroundColor: AppTheme.primaryBlue.withOpacity(0.1),
                        child: Text(
                          student.initials,
                          style: TextStyle(
                            color: AppTheme.primaryBlue,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      if (isSelected)
                        Positioned(
                          right: -2,
                          top: -2,
                          child: Container(
                            padding: const EdgeInsets.all(2),
                            decoration: const BoxDecoration(
                              color: AppTheme.primaryBlue,
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.check,
                              size: 12,
                              color: Colors.white,
                            ),
                          ),
                        ),
                    ],
                  ),
                  title: Text(
                    student.fullName,
                    style: TextStyle(
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (student.age != null)
                        Text('${student.age} ans'),
                      if (student.parentEmail != null)
                        Text(
                          student.parentEmail!,
                          style: const TextStyle(fontSize: 12),
                        ),
                    ],
                  ),
                  trailing: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: Icon(
                          Icons.assignment_ind,
                          color: AppTheme.successGreen,
                        ),
                        onPressed: () => _showAssignStudentDialog(student),
                        tooltip: 'Assigner à une classe',
                      ),
                      Checkbox(
                        value: isSelected,
                        onChanged: (value) {
                          setState(() {
                            _selectedStudents[student.id] = value ?? false;
                          });
                        },
                      ),
                    ],
                  ),
                  onTap: () {
                    setState(() {
                      _selectedStudents[student.id] = !isSelected;
                    });
                  },
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildAssignmentManagementTab(AppProvider appProvider) {
    final assignedStudents = appProvider.activeStudents.where((s) => s.classId != null).toList();
    final filteredStudents = _getFilteredAssignedStudents(assignedStudents, appProvider);

    if (filteredStudents.isEmpty) {
      return _buildEmptyState(
        icon: Icons.group_off,
        title: 'Aucun élève assigné',
        subtitle: 'Commencez par assigner des élèves aux classes',
      );
    }

    return Column(
      children: [
        // Summary header
        Container(
          padding: const EdgeInsets.all(16),
          color: Colors.grey[50],
          child: Row(
            children: [
              Icon(Icons.group, color: AppTheme.primaryBlue),
              const SizedBox(width: 8),
              Text(
                '${filteredStudents.length} élève${filteredStudents.length > 1 ? 's' : ''} assigné${filteredStudents.length > 1 ? 's' : ''}',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
        
        // Assignments List
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: filteredStudents.length,
            itemBuilder: (context, index) {
              final student = filteredStudents[index];
              final schoolClass = appProvider.getClass(student.classId!);
              
              return Card(
                margin: const EdgeInsets.only(bottom: 8),
                child: ListTile(
                  leading: CircleAvatar(
                    backgroundColor: schoolClass != null
                        ? (schoolClass.isPrimaryLevel 
                            ? AppTheme.successGreen.withOpacity(0.1)
                            : AppTheme.warningOrange.withOpacity(0.1))
                        : AppTheme.primaryBlue.withOpacity(0.1),
                    child: Text(
                      student.initials,
                      style: TextStyle(
                        color: schoolClass != null
                            ? (schoolClass.isPrimaryLevel 
                                ? AppTheme.successGreen
                                : AppTheme.warningOrange)
                            : AppTheme.primaryBlue,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  title: Text(student.fullName),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (student.age != null)
                        Text('${student.age} ans'),
                      if (schoolClass != null)
                        Container(
                          margin: const EdgeInsets.only(top: 4),
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: AppTheme.primaryBlue.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            schoolClass.displayName,
                            style: TextStyle(
                              fontSize: 12,
                              color: AppTheme.primaryBlue,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                    ],
                  ),
                  trailing: PopupMenuButton(
                    onSelected: (value) => _handleStudentAction(value, student, appProvider),
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'reassign',
                        child: Row(
                          children: [
                            Icon(Icons.swap_horiz, size: 16),
                            SizedBox(width: 8),
                            Text('Réassigner'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'unassign',
                        child: Row(
                          children: [
                            Icon(Icons.remove_circle_outline, size: 16, color: Colors.red),
                            SizedBox(width: 8),
                            Text('Désassigner', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildClassManagementTab(AppProvider appProvider) {
    final classes = appProvider.activeClasses;
    final filteredClasses = _getFilteredClasses(classes);

    if (filteredClasses.isEmpty) {
      return _buildEmptyState(
        icon: Icons.class_,
        title: 'Aucune classe',
        subtitle: 'Créez des classes pour commencer',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: filteredClasses.length,
      itemBuilder: (context, index) {
        final schoolClass = filteredClasses[index];
        final students = appProvider.getClassStudents(schoolClass.id);
        final occupancyRate = (students.length / schoolClass.maxStudents * 100).round();
        
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ExpansionTile(
            leading: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: schoolClass.isPrimaryLevel
                    ? AppTheme.successGreen.withOpacity(0.1)
                    : AppTheme.warningOrange.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                schoolClass.isPrimaryLevel ? Icons.school : Icons.account_balance,
                color: schoolClass.isPrimaryLevel
                    ? AppTheme.successGreen
                    : AppTheme.warningOrange,
                size: 20,
              ),
            ),
            title: Text(
              schoolClass.displayName,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: occupancyRate >= 90
                        ? AppTheme.accentRed.withOpacity(0.1)
                        : occupancyRate >= 70
                            ? AppTheme.warningOrange.withOpacity(0.1)
                            : AppTheme.successGreen.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    '${students.length}/${schoolClass.maxStudents}',
                    style: TextStyle(
                      fontSize: 12,
                      color: occupancyRate >= 90
                          ? AppTheme.accentRed
                          : occupancyRate >= 70
                              ? AppTheme.warningOrange
                              : AppTheme.successGreen,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  schoolClass.gradingSystemDisplayName,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
            trailing: ElevatedButton.icon(
              onPressed: () => _showAddStudentsToClassDialog(schoolClass, appProvider),
              icon: const Icon(Icons.person_add, size: 16),
              label: const Text('Ajouter'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryBlue,
                foregroundColor: Colors.white,
                minimumSize: const Size(0, 32),
              ),
            ),
            children: [
              if (students.isEmpty)
                const Padding(
                  padding: EdgeInsets.all(16),
                  child: Text(
                    'Aucun élève dans cette classe',
                    style: TextStyle(color: Colors.grey),
                    textAlign: TextAlign.center,
                  ),
                )
              else
                ...students.map((student) => ListTile(
                  dense: true,
                  leading: CircleAvatar(
                    radius: 16,
                    backgroundColor: AppTheme.primaryBlue.withOpacity(0.1),
                    child: Text(
                      student.initials,
                      style: TextStyle(
                        color: AppTheme.primaryBlue,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  title: Text(
                    student.fullName,
                    style: const TextStyle(fontSize: 14),
                  ),
                  subtitle: student.age != null 
                      ? Text('${student.age} ans', style: const TextStyle(fontSize: 12))
                      : null,
                  trailing: IconButton(
                    icon: Icon(
                      Icons.remove_circle_outline,
                      size: 16,
                      color: AppTheme.accentRed,
                    ),
                    onPressed: () => _showRemoveStudentFromClassDialog(student, schoolClass, appProvider),
                  ),
                )),
            ],
          ),
        );
      },
    );
  }

  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              title,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomActionBar() {
    final hasSelectedStudents = _selectedStudents.values.any((selected) => selected);
    
    if (!hasSelectedStudents) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.primaryBlue.withOpacity(0.1),
        border: Border(top: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Row(
        children: [
          Text(
            '${_selectedStudents.values.where((s) => s).length} élève(s) sélectionné(s)',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          const Spacer(),
          ElevatedButton.icon(
            onPressed: _selectedTargetClassId != null
                ? _assignSelectedStudents
                : _showBulkAssignDialog,
            icon: const Icon(Icons.assignment_ind),
            label: Text(_selectedTargetClassId != null ? 'Assigner' : 'Assigner à...'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryBlue,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  List<Student> _getFilteredUnassignedStudents(AppProvider appProvider) {
    var students = appProvider.activeStudents.where((s) => s.classId == null);

    if (_searchQuery.isNotEmpty) {
      students = students.where((s) =>
          s.fullName.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          (s.parentEmail?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false));
    }

    if (_selectedLevel != null) {
      // Since Student model doesn't have level, we'll filter by age or other criteria
      // This is a placeholder - you might want to add level to Student model
    }

    return students.toList();
  }

  List<Student> _getFilteredAssignedStudents(List<Student> students, AppProvider appProvider) {
    var filtered = students;

    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((s) =>
          s.fullName.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          (s.parentEmail?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false)).toList();
    }

    if (_selectedLevel != null) {
      filtered = filtered.where((s) {
        final schoolClass = appProvider.getClass(s.classId!);
        return schoolClass?.level == _selectedLevel;
      }).toList();
    }

    return filtered;
  }

  List<SchoolClass> _getFilteredClasses(List<SchoolClass> classes) {
    var filtered = classes;

    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((c) =>
          c.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          c.levelDisplayName.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          (c.teacher?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false)).toList();
    }

    if (_selectedLevel != null) {
      filtered = filtered.where((c) => c.level == _selectedLevel).toList();
    }

    return filtered;
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Rechercher'),
        content: TextField(
          autofocus: true,
          decoration: const InputDecoration(
            hintText: 'Nom, email, classe...',
            prefixIcon: Icon(Icons.search),
          ),
          onChanged: (value) {
            setState(() {
              _searchQuery = value;
            });
          },
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                _searchQuery = '';
              });
              Navigator.pop(context);
            },
            child: const Text('Effacer'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filtrer par niveau'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<EducationLevel?>(
              title: const Text('Tous les niveaux'),
              value: null,
              groupValue: _selectedLevel,
              onChanged: (value) {
                setState(() {
                  _selectedLevel = value;
                });
                Navigator.pop(context);
              },
            ),
            ...EducationLevel.values.map((level) => RadioListTile<EducationLevel>(
              title: Text(level.name.toUpperCase()),
              value: level,
              groupValue: _selectedLevel,
              onChanged: (value) {
                setState(() {
                  _selectedLevel = value;
                });
                Navigator.pop(context);
              },
            )),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  void _showAssignStudentDialog(Student student) {
    final appProvider = context.read<AppProvider>();
    final availableClasses = appProvider.activeClasses;

    if (availableClasses.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Aucune classe disponible. Créez d\'abord une classe.'),
          backgroundColor: AppTheme.warningOrange,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Assigner ${student.fullName}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Choisissez une classe :'),
            const SizedBox(height: 16),
            ...availableClasses.map((schoolClass) {
              final students = appProvider.getClassStudents(schoolClass.id);
              final isFull = students.length >= schoolClass.maxStudents;
              
              return ListTile(
                leading: Icon(
                  schoolClass.isPrimaryLevel ? Icons.school : Icons.account_balance,
                  color: isFull ? Colors.grey : AppTheme.primaryBlue,
                ),
                title: Text(
                  schoolClass.displayName,
                  style: TextStyle(
                    color: isFull ? Colors.grey : null,
                  ),
                ),
                subtitle: Text(
                  '${students.length}/${schoolClass.maxStudents} élèves',
                  style: TextStyle(
                    color: isFull ? Colors.red : Colors.grey[600],
                  ),
                ),
                enabled: !isFull,
                onTap: isFull ? null : () async {
                  await _assignStudentToClass(student, schoolClass);
                  Navigator.pop(context);
                },
              );
            }),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
        ],
      ),
    );
  }

  void _showBulkAssignDialog() {
    final appProvider = context.read<AppProvider>();
    final availableClasses = appProvider.activeClasses;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Assigner les élèves sélectionnés'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Choisissez une classe pour tous les élèves sélectionnés :'),
            const SizedBox(height: 16),
            ...availableClasses.map((schoolClass) {
              final students = appProvider.getClassStudents(schoolClass.id);
              final selectedCount = _selectedStudents.values.where((s) => s).length;
              final wouldExceed = students.length + selectedCount > schoolClass.maxStudents;
              
              return ListTile(
                leading: Icon(
                  schoolClass.isPrimaryLevel ? Icons.school : Icons.account_balance,
                  color: wouldExceed ? Colors.grey : AppTheme.primaryBlue,
                ),
                title: Text(
                  schoolClass.displayName,
                  style: TextStyle(
                    color: wouldExceed ? Colors.grey : null,
                  ),
                ),
                subtitle: Text(
                  '${students.length}/${schoolClass.maxStudents} élèves${wouldExceed ? ' (Dépasserait la limite)' : ''}',
                  style: TextStyle(
                    color: wouldExceed ? Colors.red : Colors.grey[600],
                  ),
                ),
                enabled: !wouldExceed,
                onTap: wouldExceed ? null : () async {
                  await _assignSelectedStudentsToClass(schoolClass);
                  Navigator.pop(context);
                },
              );
            }),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
        ],
      ),
    );
  }

  void _showAddStudentsToClassDialog(SchoolClass schoolClass, AppProvider appProvider) {
    final unassignedStudents = appProvider.activeStudents.where((s) => s.classId == null).toList();
    
    if (unassignedStudents.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Aucun élève non assigné disponible.'),
          backgroundColor: AppTheme.warningOrange,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AddStudentsToClassDialog(
        schoolClass: schoolClass,
        availableStudents: unassignedStudents,
      ),
    );
  }

  void _showRemoveStudentFromClassDialog(Student student, SchoolClass schoolClass, AppProvider appProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Retirer de la classe'),
        content: Text('Retirer ${student.fullName} de la classe ${schoolClass.displayName} ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () async {
              await _unassignStudentFromClass(student);
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.accentRed,
              foregroundColor: Colors.white,
            ),
            child: const Text('Retirer'),
          ),
        ],
      ),
    );
  }

  void _handleStudentAction(String action, Student student, AppProvider appProvider) {
    switch (action) {
      case 'reassign':
        _showAssignStudentDialog(student);
        break;
      case 'unassign':
        _showRemoveStudentFromClassDialog(student, appProvider.getClass(student.classId!)!, appProvider);
        break;
    }
  }

  Future<void> _assignStudentToClass(Student student, SchoolClass schoolClass) async {
    try {
      final updatedStudent = Student(
        id: student.id,
        firstName: student.firstName,
        lastName: student.lastName,
        photoPath: student.photoPath,
        dateOfBirth: student.dateOfBirth,
        parentEmail: student.parentEmail,
        parentPhone: student.parentPhone,
        notes: student.notes,
        createdAt: student.createdAt,
        updatedAt: DateTime.now(),
        isActive: student.isActive,
        classId: schoolClass.id,
      );

      await context.read<AppProvider>().updateStudent(updatedStudent);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${student.fullName} assigné à ${schoolClass.displayName}'),
            backgroundColor: AppTheme.successGreen,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur: $e'),
            backgroundColor: AppTheme.accentRed,
          ),
        );
      }
    }
  }

  Future<void> _unassignStudentFromClass(Student student) async {
    try {
      final updatedStudent = Student(
        id: student.id,
        firstName: student.firstName,
        lastName: student.lastName,
        photoPath: student.photoPath,
        dateOfBirth: student.dateOfBirth,
        parentEmail: student.parentEmail,
        parentPhone: student.parentPhone,
        notes: student.notes,
        createdAt: student.createdAt,
        updatedAt: DateTime.now(),
        isActive: student.isActive,
        classId: null, // Remove class assignment
      );

      await context.read<AppProvider>().updateStudent(updatedStudent);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${student.fullName} retiré de la classe'),
            backgroundColor: AppTheme.warningOrange,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur: $e'),
            backgroundColor: AppTheme.accentRed,
          ),
        );
      }
    }
  }

  Future<void> _assignSelectedStudents() async {
    if (_selectedTargetClassId == null) return;
    
    final appProvider = context.read<AppProvider>();
    final targetClass = appProvider.getClass(_selectedTargetClassId!);
    
    if (targetClass == null) return;
    
    await _assignSelectedStudentsToClass(targetClass);
  }

  Future<void> _assignSelectedStudentsToClass(SchoolClass targetClass) async {
    try {
      final appProvider = context.read<AppProvider>();
      final selectedStudentIds = _selectedStudents.entries
          .where((entry) => entry.value)
          .map((entry) => entry.key)
          .toList();

      for (final studentId in selectedStudentIds) {
        final student = appProvider.getStudent(studentId);
        if (student != null) {
          await _assignStudentToClass(student, targetClass);
        }
      }

      setState(() {
        _selectedStudents.clear();
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${selectedStudentIds.length} élève(s) assigné(s) à ${targetClass.displayName}'),
            backgroundColor: AppTheme.successGreen,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur: $e'),
            backgroundColor: AppTheme.accentRed,
          ),
        );
      }
    }
  }
}

// Helper dialog for adding multiple students to a class
class AddStudentsToClassDialog extends StatefulWidget {
  final SchoolClass schoolClass;
  final List<Student> availableStudents;

  const AddStudentsToClassDialog({
    super.key,
    required this.schoolClass,
    required this.availableStudents,
  });

  @override
  State<AddStudentsToClassDialog> createState() => _AddStudentsToClassDialogState();
}

class _AddStudentsToClassDialogState extends State<AddStudentsToClassDialog> {
  final Map<String, bool> _selectedStudents = {};

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Ajouter des élèves à ${widget.schoolClass.displayName}'),
      content: SizedBox(
        width: double.maxFinite,
        height: 400,
        child: Column(
          children: [
            Text(
              '${widget.availableStudents.length} élève(s) disponible(s)',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: ListView.builder(
                itemCount: widget.availableStudents.length,
                itemBuilder: (context, index) {
                  final student = widget.availableStudents[index];
                  final isSelected = _selectedStudents[student.id] ?? false;
                  
                  return CheckboxListTile(
                    value: isSelected,
                    onChanged: (value) {
                      setState(() {
                        _selectedStudents[student.id] = value ?? false;
                      });
                    },
                    title: Text(student.fullName),
                    subtitle: student.age != null ? Text('${student.age} ans') : null,
                    secondary: CircleAvatar(
                      backgroundColor: AppTheme.primaryBlue.withOpacity(0.1),
                      child: Text(
                        student.initials,
                        style: TextStyle(
                          color: AppTheme.primaryBlue,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Annuler'),
        ),
        ElevatedButton(
          onPressed: _selectedStudents.values.any((selected) => selected)
              ? () async {
                  await _assignSelectedStudents();
                  Navigator.pop(context);
                }
              : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.primaryBlue,
            foregroundColor: Colors.white,
          ),
          child: Text('Ajouter (${_selectedStudents.values.where((s) => s).length})'),
        ),
      ],
    );
  }

  Future<void> _assignSelectedStudents() async {
    try {
      final appProvider = context.read<AppProvider>();
      final selectedStudentIds = _selectedStudents.entries
          .where((entry) => entry.value)
          .map((entry) => entry.key)
          .toList();

      for (final studentId in selectedStudentIds) {
        final student = widget.availableStudents.firstWhere((s) => s.id == studentId);
        
        final updatedStudent = Student(
          id: student.id,
          firstName: student.firstName,
          lastName: student.lastName,
          photoPath: student.photoPath,
          dateOfBirth: student.dateOfBirth,
          parentEmail: student.parentEmail,
          parentPhone: student.parentPhone,
          notes: student.notes,
          createdAt: student.createdAt,
          updatedAt: DateTime.now(),
          isActive: student.isActive,
          classId: widget.schoolClass.id,
        );

        await appProvider.updateStudent(updatedStudent);
      }

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${selectedStudentIds.length} élève(s) ajouté(s) à ${widget.schoolClass.displayName}'),
            backgroundColor: AppTheme.successGreen,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur: $e'),
            backgroundColor: AppTheme.accentRed,
          ),
        );
      }
    }
  }
}
