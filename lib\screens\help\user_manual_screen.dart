import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/app_provider.dart';
import '../../services/license_service.dart';

class UserManualScreen extends StatefulWidget {
  const UserManualScreen({super.key});

  @override
  State<UserManualScreen> createState() => _UserManualScreenState();
}

class _UserManualScreenState extends State<UserManualScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  int _currentIndex = 0;

  final List<ManualSection> _sections = [
    ManualSection(
      title: 'Démarrage',
      icon: Icons.play_arrow,
      content: _getStartedContent(),
    ),
    ManualSection(
      title: 'Classes',
      icon: Icons.class_,
      content: _classesContent(),
    ),
    ManualSection(
      title: 'Élèves',
      icon: Icons.people,
      content: _studentsContent(),
    ),
    ManualSection(
      title: 'Matières',
      icon: Icons.subject,
      content: _subjectsContent(),
    ),
    ManualSection(title: 'Cours', icon: Icons.book, content: _coursesContent()),
    ManualSection(
      title: 'Leçons',
      icon: Icons.schedule,
      content: _lessonsContent(),
    ),
    ManualSection(title: 'Notes', icon: Icons.grade, content: _gradesContent()),
    ManualSection(
      title: 'Présences',
      icon: Icons.event_available,
      content: _attendanceContent(),
    ),
    ManualSection(
      title: 'Comportement',
      icon: Icons.psychology,
      content: _behaviorContent(),
    ),
    ManualSection(
      title: 'Paramètres',
      icon: Icons.settings,
      content: _settingsContent(),
    ),
    ManualSection(
      title: 'Licence',
      icon: Icons.verified,
      content: _licenseContent(),
    ),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _sections.length, vsync: this);
    _tabController.addListener(() {
      setState(() {
        _currentIndex = _tabController.index;
      });
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Manuel d\'utilisation'),
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: _sections.map((section) {
            return Tab(icon: Icon(section.icon), text: section.title);
          }).toList(),
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: _sections.map((section) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSectionHeader(section),
                const SizedBox(height: 20),
                ...section.content,
              ],
            ),
          );
        }).toList(),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showQuickHelp(),
        icon: const Icon(Icons.help),
        label: const Text('Aide rapide'),
      ),
    );
  }

  Widget _buildSectionHeader(ManualSection section) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).primaryColor.withOpacity(0.1),
            Theme.of(context).primaryColor.withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).primaryColor.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(section.icon, color: Colors.white, size: 24),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  section.title,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Guide complet pour ${section.title.toLowerCase()}',
                  style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showQuickHelp() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.help, color: Colors.blue),
            SizedBox(width: 8),
            Text('Aide rapide'),
          ],
        ),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Actions rapides:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('• Appuyez sur + pour ajouter des éléments'),
              Text('• Glissez pour voir plus d\'options'),
              Text('• Appuyez longuement pour les actions rapides'),
              Text('• Utilisez la recherche en haut des listes'),
              SizedBox(height: 16),
              Text(
                'Navigation:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('• Tableau de bord: Statistiques et actions rapides'),
              Text('• Boutons d\'action: Accès direct aux fonctionnalités'),
              Text('• Icônes en haut: Thème, aide et paramètres'),
              SizedBox(height: 16),
              Text(
                'Besoin d\'aide?',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('Contactez le support via les paramètres.'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  static List<Widget> _getStartedContent() {
    return [
      const ManualCard(
        title: 'Bienvenue dans isoucklou',
        content:
            'Cette application vous aide à gérer efficacement votre classe avec des outils modernes et intuitifs.',
        icon: Icons.waving_hand,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Premier démarrage',
        content:
            '1. Configurez vos informations dans Paramètres\n'
            '2. Créez une classe selon votre niveau d\'enseignement\n'
            '3. Ajoutez des matières\n'
            '4. Inscrivez vos élèves\n'
            '5. Créez vos cours et leçons\n'
            '6. Commencez à utiliser l\'application!',
        icon: Icons.checklist,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Navigation',
        content:
            'Le tableau de bord affiche un aperçu général avec les statistiques, '
            'actions rapides et activité récente. Utilisez les boutons d\'action '
            'rapides pour accéder directement aux fonctionnalités principales.',
        icon: Icons.navigation,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Conseils',
        content:
            '• Commencez par créer vos classes selon le système français\n'
            '• Le mode clair/sombre s\'adapte à votre environnement\n'
            '• Les données sont sauvegardées automatiquement\n'
            '• Utilisez le système de notation adapté (compétences/notes)',
        icon: Icons.lightbulb,
        isHighlighted: true,
      ),
    ];
  }

  static List<Widget> _classesContent() {
    return [
      const ManualCard(
        title: 'Gestion des classes',
        content:
            'Créez et organisez vos classes selon le système éducatif français. '
            'Chaque classe a un système de notation approprié à son niveau.',
        icon: Icons.class_,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Créer une classe',
        content:
            '1. Appuyez sur le bouton +\n'
            '2. Choisissez le nom et niveau (CP à Terminale)\n'
            '3. Le système de notation s\'adapte automatiquement\n'
            '4. Définissez la capacité et localisation\n'
            '5. Ajoutez une description si nécessaire',
        icon: Icons.add,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Systèmes de notation',
        content:
            '• École Primaire (CP-CM2): Évaluation par compétences\n'
            '• Collège et Lycée (6ème-Terminale): Notes sur 20\n'
            '• Adaptation automatique selon le niveau choisi',
        icon: Icons.grade,
        isHighlighted: true,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Gestion des élèves',
        content:
            'Assignez des élèves à vos classes depuis la section Classes. '
            'Visualisez les statistiques et analyses de chaque classe.',
        icon: Icons.people,
      ),
    ];
  }

  static List<Widget> _studentsContent() {
    return [
      const ManualCard(
        title: 'Gestion des élèves',
        content:
            'Ajoutez, modifiez et organisez les informations de vos élèves.',
        icon: Icons.people,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Ajouter un élève',
        content:
            '1. Appuyez sur le bouton +\n'
            '2. Remplissez les informations obligatoires\n'
            '3. Ajoutez une photo (optionnel)\n'
            '4. Enregistrez',
        icon: Icons.person_add,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Informations élève',
        content:
            'Nom, prénom, date de naissance, contacts parents, notes personnelles, photo (optionnelle).',
        icon: Icons.info,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Attribution aux classes',
        content:
            'Les élèves peuvent être assignés à une classe depuis l\'écran de gestion des classes. '
            'Un élève ne peut être inscrit que dans une seule classe à la fois.',
        icon: Icons.group_add,
      ),
      const SizedBox(height: 16),
      const LimitationCard(
        title: 'Limitation version gratuite',
        content: 'Maximum 10 élèves dans la version gratuite.',
        upgradeFeature: 'unlimited_students',
      ),
    ];
  }

  static List<Widget> _subjectsContent() {
    return [
      const ManualCard(
        title: 'Gestion des matières',
        content: 'Organisez vos matières selon les catégories du système français.',
        icon: Icons.subject,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Créer une matière',
        content:
            '1. Appuyez sur le bouton +\n'
            '2. Donnez un nom et code à la matière\n'
            '3. Choisissez la catégorie appropriée\n'
            '4. Associez une couleur pour l\'organisation\n'
            '5. Ajoutez une description',
        icon: Icons.add_box,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Catégories disponibles',
        content:
            'Français, Mathématiques, Sciences, Histoire-Géographie, '
            'Langues vivantes, Arts, EPS, Technologie, Éducation civique, '
            'Philosophie, Spécialités, et plus.',
        icon: Icons.category,
      ),
    ];
  }

  static List<Widget> _coursesContent() {
    return [
      const ManualCard(
        title: 'Gestion des cours',
        content: 'Organisez vos cours par matières et niveaux.',
        icon: Icons.book,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Créer un cours',
        content:
            '1. Sélectionnez une matière existante\n'
            '2. Définissez le niveau d\'éducation\n'
            '3. Ajoutez les objectifs pédagogiques\n'
            '4. Configurez la difficulté et durée estimée\n'
            '5. Ajoutez des prérequis si nécessaire',
        icon: Icons.add_box,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Objectifs d\'apprentissage',
        content:
            'Chaque cours peut avoir plusieurs objectifs pédagogiques. '
            'Suivez la progression et marquez les objectifs comme atteints.',
        icon: Icons.flag,
      ),
      const SizedBox(height: 16),
      const LimitationCard(
        title: 'Limitation version gratuite',
        content: 'Maximum 5 cours dans la version gratuite.',
        upgradeFeature: 'unlimited_courses',
      ),
    ];
  }

  static List<Widget> _lessonsContent() {
    return [
      const ManualCard(
        title: 'Gestion des leçons',
        content: 'Planifiez et organisez vos leçons avec emploi du temps intégré.',
        icon: Icons.schedule,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Créer une leçon',
        content:
            '1. Définissez le titre et la matière\n'
            '2. Choisissez la date et l\'heure\n'
            '3. Sélectionnez la salle de classe\n'
            '4. Ajoutez les objectifs et contenu\n'
            '5. Planifiez les devoirs éventuels',
        icon: Icons.add_circle,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Emploi du temps',
        content:
            'Visualisez votre planning hebdomadaire. '
            'Démarrez et terminez les leçons directement depuis l\'interface.',
        icon: Icons.calendar_view_week,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Suivi des leçons',
        content:
            'Marquez les leçons comme planifiées, en cours ou terminées. '
            'Ajoutez des notes et pièces jointes.',
        icon: Icons.task_alt,
      ),
    ];
  }

  static List<Widget> _gradesContent() {
    return [
      const ManualCard(
        title: 'Systèmes de notation',
        content:
            'Deux systèmes selon le niveau:\n'
            '• Primaire: Évaluation par compétences (Non acquis à Dépassé)\n'
            '• Secondaire: Notes numériques sur 20 avec appréciations automatiques',
        icon: Icons.grade,
        isHighlighted: true,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Ajouter une note',
        content:
            '1. Sélectionnez l\'élève et la matière\n'
            '2. Choisissez le type d\'évaluation\n'
            '3. Saisissez la note (selon le système de la classe)\n'
            '4. Ajoutez un commentaire et coefficient\n'
            '5. Pour le primaire: évaluez les compétences',
        icon: Icons.add_circle,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Types d\'évaluation',
        content:
            'Contrôle écrit, devoir maison, oral, projet, participation, '
            'évaluation de compétences, interrogation surprise, etc.',
        icon: Icons.assignment,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Rubriques d\'évaluation',
        content:
            'Créez des grilles d\'évaluation détaillées pour les projets complexes. '
            'Idéal pour les évaluations par compétences.',
        icon: Icons.grid_view,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Analyses et statistiques',
        content:
            'Suivez les moyennes par élève et par matière. '
            'Visualisez les progressions et identifiez les difficultés.',
        icon: Icons.analytics,
      ),
    ];
  }

  static List<Widget> _attendanceContent() {
    return [
      const ManualCard(
        title: 'Suivi des présences',
        content: 'Enregistrez facilement les présences de vos élèves.',
        icon: Icons.event_available,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Prise de présence',
        content:
            '1. Accédez à l\'écran des présences\n'
            '2. Sélectionnez la date du jour\n'
            '3. Marquez présent/absent/retard/excusé d\'un simple tap\n'
            '4. Ajoutez des notes si nécessaire\n'
            '5. Sauvegarde automatique',
        icon: Icons.check_circle,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Historique',
        content:
            'Consultez l\'historique des présences par élève ou par période.',
        icon: Icons.history,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Rapports de présence',
        content:
            'Consultez les taux de présence hebdomadaires et mensuels. '
            'Identifiez les élèves avec absences répétées.',
        icon: Icons.bar_chart,
      ),
    ];
  }

  static List<Widget> _behaviorContent() {
    return [
      const ManualCard(
        title: 'Notes de comportement',
        content: 'Suivez le comportement et les incidents de vos élèves.',
        icon: Icons.psychology,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Ajouter une note',
        content:
            '1. Sélectionnez l\'élève\n'
            '2. Choisissez le type (positif/négatif)\n'
            '3. Décrivez l\'incident\n'
            '4. Ajoutez des actions prises',
        icon: Icons.note_add,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Types de comportement',
        content:
            'Positif: participation, aide aux camarades, progrès notable\n'
            'Négatif: perturbation, travail non fait, retard\n'
            'Neutre: observations générales',
        icon: Icons.category,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Modèles de commentaires',
        content:
            'Utilisez les modèles prédéfinis ou créez vos propres modèles '
            'pour gagner du temps dans la saisie des observations.',
        icon: Icons.text_snippet,
      ),
    ];
  }

  static List<Widget> _settingsContent() {
    return [
      const ManualCard(
        title: 'Configuration',
        content: 'Personnalisez l\'application selon vos préférences.',
        icon: Icons.settings,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Informations personnelles',
        content: 'Nom, école, année scolaire - affiché dans les rapports.',
        icon: Icons.person,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Thème et apparence',
        content:
            'Basculez entre mode clair et sombre selon votre environnement. '
            'Le thème s\'adapte automatiquement pour une meilleure lisibilité.',
        icon: Icons.palette,
        isHighlighted: true,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Préréglages des notes',
        content:
            'Définissez des matières et titres par défaut pour accélérer '
            'la saisie des évaluations.',
        icon: Icons.speed,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Sauvegarde',
        content: 'Export des données, sauvegarde automatique (premium).',
        icon: Icons.backup,
      ),
    ];
  }

  static List<Widget> _licenseContent() {
    return [
      const ManualCard(
        title: 'Version gratuite vs Premium',
        content:
            'Découvrez les fonctionnalités disponibles selon votre licence.',
        icon: Icons.verified,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Version gratuite',
        content:
            '• Maximum 10 élèves\n'
            '• Maximum 5 cours\n'
            '• Fonctionnalités de base\n'
            '• Pas d\'export de données',
        icon: Icons.free_breakfast,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Version Premium',
        content:
            '• Élèves et cours illimités\n'
            '• Export de données\n'
            '• Sauvegarde automatique\n'
            '• Rapports avancés\n'
            '• Support prioritaire',
        icon: Icons.star,
        isHighlighted: true,
      ),
      const SizedBox(height: 16),
      const UpgradeCard(),
    ];
  }
}

class ManualSection {
  final String title;
  final IconData icon;
  final List<Widget> content;

  ManualSection({
    required this.title,
    required this.icon,
    required this.content,
  });
}

class ManualCard extends StatelessWidget {
  final String title;
  final String content;
  final IconData icon;
  final bool isHighlighted;

  const ManualCard({
    super.key,
    required this.title,
    required this.content,
    required this.icon,
    this.isHighlighted = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isHighlighted
            ? Theme.of(context).primaryColor.withOpacity(0.1)
            : Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isHighlighted
              ? Theme.of(context).primaryColor.withOpacity(0.3)
              : Colors.grey.withOpacity(0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: isHighlighted
                  ? Theme.of(context).primaryColor
                  : Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: isHighlighted ? Colors.white : Colors.grey[600],
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: isHighlighted
                        ? Theme.of(context).primaryColor
                        : null,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  content,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[700],
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class LimitationCard extends StatelessWidget {
  final String title;
  final String content;
  final String upgradeFeature;

  const LimitationCard({
    super.key,
    required this.title,
    required this.content,
    required this.upgradeFeature,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, appProvider, child) {
        if (appProvider.isLicensed) {
          return const SizedBox.shrink();
        }

        return Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.orange[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.orange[200]!),
          ),
          child: Row(
            children: [
              const Icon(Icons.lock, color: Colors.orange, size: 24),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.orange,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      content,
                      style: TextStyle(fontSize: 14, color: Colors.orange[800]),
                    ),
                  ],
                ),
              ),
              TextButton(
                onPressed: () {
                  appProvider.showRestrictionDialog(context, upgradeFeature);
                },
                child: const Text('Débloquer'),
              ),
            ],
          ),
        );
      },
    );
  }
}

class UpgradeCard extends StatelessWidget {
  const UpgradeCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, appProvider, child) {
        if (appProvider.isLicensed) {
          return Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.green[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.green[200]!),
            ),
            child: const Row(
              children: [
                Icon(Icons.verified, color: Colors.green, size: 24),
                SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Version Premium activée',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        'Merci pour votre soutien! 🎉',
                        style: TextStyle(fontSize: 14, color: Colors.green),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        }

        return Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.orange[100]!, Colors.orange[50]!],
            ),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.orange[200]!),
          ),
          child: Column(
            children: [
              const Row(
                children: [
                  Icon(Icons.coffee, color: Colors.orange, size: 24),
                  SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Soutenez le développement',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.orange,
                          ),
                        ),
                        SizedBox(height: 4),
                        Text(
                          'Achetez un café au développeur pour débloquer toutes les fonctionnalités!',
                          style: TextStyle(fontSize: 14, color: Colors.orange),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: () {
                    LicenseService.showRestrictionDialog(
                      context,
                      'premium_upgrade',
                    );
                  },
                  icon: const Icon(Icons.coffee),
                  label: const Text('Acheter un café ☕'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
