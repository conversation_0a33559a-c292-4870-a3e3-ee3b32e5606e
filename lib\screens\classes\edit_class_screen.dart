import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/app_provider.dart';
import '../../models/class.dart';
import '../../models/course.dart';
import '../../utils/app_theme.dart';

class EditClassScreen extends StatefulWidget {
  final String classId;

  const EditClassScreen({super.key, required this.classId});

  @override
  State<EditClassScreen> createState() => _EditClassScreenState();
}

class _EditClassScreenState extends State<EditClassScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _teacherController = TextEditingController();
  final _classroomController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _notesController = TextEditingController();

  SchoolClass? _schoolClass;
  EducationLevel? _selectedLevel;
  GradingSystemType? _selectedGradingSystem;
  int _maxStudents = 30;
  String? _academicYear;
  bool _isLoading = false;
  bool _isActive = true;

  // Predefined academic years
  final List<String> _academicYears = [
    '2024-2025',
    '2025-2026',
    '2026-2027',
    '2027-2028',
  ];

  @override
  void initState() {
    super.initState();
    _loadClass();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _teacherController.dispose();
    _classroomController.dispose();
    _descriptionController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  void _loadClass() {
    final appProvider = context.read<AppProvider>();
    _schoolClass = appProvider.getClass(widget.classId);
    
    if (_schoolClass != null) {
      _nameController.text = _schoolClass!.name;
      _teacherController.text = _schoolClass!.teacher ?? '';
      _classroomController.text = _schoolClass!.classroom ?? '';
      _descriptionController.text = _schoolClass!.description ?? '';
      _notesController.text = _schoolClass!.notes ?? '';
      _selectedLevel = _schoolClass!.level;
      _selectedGradingSystem = _schoolClass!.gradingSystem;
      _maxStudents = _schoolClass!.maxStudents;
      _academicYear = _schoolClass!.academicYear;
      _isActive = _schoolClass!.isActive;
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_schoolClass == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('Classe introuvable')),
        body: const Center(
          child: Text('Cette classe n\'existe pas ou a été supprimée.'),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Modifier la classe'),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveClass,
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Enregistrer'),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            _buildBasicInfoSection(),
            const SizedBox(height: 24),
            _buildEducationLevelSection(),
            const SizedBox(height: 24),
            _buildCapacitySection(),
            const SizedBox(height: 24),
            _buildDetailsSection(),
            const SizedBox(height: 24),
            _buildNotesSection(),
            const SizedBox(height: 24),
            _buildStatusSection(),
            const SizedBox(height: 24),
            _buildGradingSystemInfo(),
            const SizedBox(height: 32),
            _buildDeleteSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Informations de base',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Nom de la classe *',
                prefixIcon: Icon(Icons.class_),
                hintText: 'Ex: CP-A, 6ème B, Terminale S...',
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Le nom de la classe est obligatoire';
                }
                return null;
              },
              textCapitalization: TextCapitalization.words,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _teacherController,
              decoration: const InputDecoration(
                labelText: 'Enseignant principal',
                prefixIcon: Icon(Icons.person),
                hintText: 'Nom de l\'enseignant principal',
              ),
              textCapitalization: TextCapitalization.words,
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _academicYear,
              decoration: const InputDecoration(
                labelText: 'Année scolaire',
                prefixIcon: Icon(Icons.calendar_today),
              ),
              items: _academicYears.map((year) {
                return DropdownMenuItem(
                  value: year,
                  child: Text(year),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _academicYear = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEducationLevelSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Niveau d\'éducation',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppTheme.warningOrange.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: AppTheme.warningOrange.withOpacity(0.3)),
              ),
              child: Row(
                children: [
                  Icon(Icons.warning, color: AppTheme.warningOrange, size: 20),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      'Attention: Changer le niveau d\'éducation peut affecter le système de notation existant.',
                      style: TextStyle(fontSize: 12),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            
            // Primary levels
            _buildLevelCategory(
              'École Primaire (Compétences)',
              [
                EducationLevel.cp,
                EducationLevel.ce1,
                EducationLevel.ce2,
                EducationLevel.cm1,
                EducationLevel.cm2,
              ],
              AppTheme.successGreen,
            ),
            
            const SizedBox(height: 16),
            
            // Secondary levels
            _buildLevelCategory(
              'Collège et Lycée (Notes /20)',
              [
                EducationLevel.sixieme,
                EducationLevel.cinquieme,
                EducationLevel.quatrieme,
                EducationLevel.troisieme,
                EducationLevel.seconde,
                EducationLevel.premiere,
                EducationLevel.terminale,
              ],
              AppTheme.warningOrange,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLevelCategory(String title, List<EducationLevel> levels, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: levels.map((level) {
              final isSelected = _selectedLevel == level;
              return ChoiceChip(
                label: Text(_getLevelDisplayName(level)),
                selected: isSelected,
                onSelected: (selected) {
                  if (selected) {
                    setState(() {
                      _selectedLevel = level;
                      // Auto-set grading system based on level
                      _selectedGradingSystem = _isPrimaryLevel(level)
                          ? GradingSystemType.primary
                          : GradingSystemType.secondary;
                    });
                  }
                },
                selectedColor: color.withOpacity(0.2),
                checkmarkColor: color,
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildCapacitySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Capacité et localisation',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Nombre maximum d\'élèves: $_maxStudents',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Slider(
                        value: _maxStudents.toDouble(),
                        min: 10,
                        max: 40,
                        divisions: 30,
                        label: _maxStudents.toString(),
                        onChanged: (value) {
                          setState(() {
                            _maxStudents = value.round();
                          });
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _classroomController,
              decoration: const InputDecoration(
                labelText: 'Salle de classe',
                prefixIcon: Icon(Icons.room),
                hintText: 'Ex: Salle 101, Bâtiment A...',
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Description',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description de la classe',
                prefixIcon: Icon(Icons.description),
                hintText: 'Brève description des objectifs, spécificités...',
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Notes additionnelles',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'Notes privées',
                prefixIcon: Icon(Icons.note),
                hintText: 'Informations supplémentaires, rappels...',
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Statut de la classe',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('Classe active'),
              subtitle: Text(
                _isActive 
                    ? 'La classe est active et visible dans l\'application'
                    : 'La classe est archivée et masquée',
              ),
              value: _isActive,
              onChanged: (value) {
                setState(() {
                  _isActive = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGradingSystemInfo() {
    if (_selectedGradingSystem == null) {
      return const SizedBox.shrink();
    }

    final isPrimary = _selectedGradingSystem == GradingSystemType.primary;
    
    return Card(
      color: isPrimary 
          ? AppTheme.successGreen.withOpacity(0.05)
          : AppTheme.warningOrange.withOpacity(0.05),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isPrimary ? Icons.stars : Icons.grade,
                  color: isPrimary ? AppTheme.successGreen : AppTheme.warningOrange,
                ),
                const SizedBox(width: 8),
                Text(
                  'Système de notation',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: isPrimary ? AppTheme.successGreen : AppTheme.warningOrange,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              isPrimary
                  ? 'Évaluation par compétences'
                  : 'Notation traditionnelle sur 20',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              isPrimary
                  ? 'Les élèves seront évalués selon 4 niveaux :\n• Non acquis\n• En cours d\'acquisition\n• Acquis\n• Dépassé'
                  : 'Les élèves recevront des notes numériques de 0 à 20 avec des appréciations automatiques selon les seuils du système français.',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[700],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDeleteSection() {
    return Card(
      color: AppTheme.accentRed.withOpacity(0.05),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.delete_forever, color: AppTheme.accentRed),
                const SizedBox(width: 8),
                Text(
                  'Zone dangereuse',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.accentRed,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              'Attention: Ces actions sont irréversibles',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _archiveClass,
                    icon: const Icon(Icons.archive),
                    label: const Text('Archiver'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: AppTheme.warningOrange,
                      side: BorderSide(color: AppTheme.warningOrange),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _deleteClass,
                    icon: const Icon(Icons.delete_forever),
                    label: const Text('Supprimer'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.accentRed,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _getLevelDisplayName(EducationLevel level) {
    switch (level) {
      case EducationLevel.cp:
        return 'CP';
      case EducationLevel.ce1:
        return 'CE1';
      case EducationLevel.ce2:
        return 'CE2';
      case EducationLevel.cm1:
        return 'CM1';
      case EducationLevel.cm2:
        return 'CM2';
      case EducationLevel.sixieme:
        return '6ème';
      case EducationLevel.cinquieme:
        return '5ème';
      case EducationLevel.quatrieme:
        return '4ème';
      case EducationLevel.troisieme:
        return '3ème';
      case EducationLevel.seconde:
        return '2nde';
      case EducationLevel.premiere:
        return '1ère';
      case EducationLevel.terminale:
        return 'Terminale';
    }
  }

  bool _isPrimaryLevel(EducationLevel level) {
    return [
      EducationLevel.cp,
      EducationLevel.ce1,
      EducationLevel.ce2,
      EducationLevel.cm1,
      EducationLevel.cm2,
    ].contains(level);
  }

  Future<void> _saveClass() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedLevel == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Veuillez sélectionner un niveau d\'éducation'),
          backgroundColor: AppTheme.accentRed,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final updatedClass = _schoolClass!.copyWith(
        name: _nameController.text.trim(),
        level: _selectedLevel!,
        gradingSystem: _selectedGradingSystem!,
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
        teacher: _teacherController.text.trim().isEmpty
            ? null
            : _teacherController.text.trim(),
        maxStudents: _maxStudents,
        classroom: _classroomController.text.trim().isEmpty
            ? null
            : _classroomController.text.trim(),
        academicYear: _academicYear,
        notes: _notesController.text.trim().isEmpty
            ? null
            : _notesController.text.trim(),
        isActive: _isActive,
      );

      final appProvider = context.read<AppProvider>();
      await appProvider.updateClass(updatedClass);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Classe "${updatedClass.displayName}" modifiée avec succès'),
            backgroundColor: AppTheme.successGreen,
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la modification: $e'),
            backgroundColor: AppTheme.accentRed,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _archiveClass() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Archiver la classe'),
        content: Text(
          'Êtes-vous sûr de vouloir archiver la classe "${_schoolClass!.displayName}" ?\n\nUne classe archivée sera masquée mais pourra être restaurée plus tard.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                final archivedClass = _schoolClass!.copyWith(isActive: false);
                final appProvider = context.read<AppProvider>();
                await appProvider.updateClass(archivedClass);
                
                if (mounted) {
                  Navigator.pop(context); // Close dialog
                  Navigator.pop(context); // Close edit screen
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Classe "${_schoolClass!.displayName}" archivée'),
                      backgroundColor: AppTheme.warningOrange,
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Erreur: $e'),
                      backgroundColor: AppTheme.accentRed,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.warningOrange,
              foregroundColor: Colors.white,
            ),
            child: const Text('Archiver'),
          ),
        ],
      ),
    );
  }

  void _deleteClass() {
    final appProvider = context.read<AppProvider>();
    final students = appProvider.getClassStudents(_schoolClass!.id);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Supprimer définitivement'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Êtes-vous sûr de vouloir supprimer définitivement la classe "${_schoolClass!.displayName}" ?',
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppTheme.accentRed.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: AppTheme.accentRed.withOpacity(0.3)),
              ),
              child: Row(
                children: [
                  Icon(Icons.warning, color: AppTheme.accentRed, size: 20),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      'Cette action est irréversible !',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            if (students.isNotEmpty) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppTheme.warningOrange.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppTheme.warningOrange.withOpacity(0.3)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${students.length} élève(s) assigné(s)',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: AppTheme.warningOrange,
                      ),
                    ),
                    const SizedBox(height: 4),
                    const Text(
                      'Les élèves ne seront pas supprimés mais ne seront plus assignés à aucune classe.',
                      style: TextStyle(fontSize: 12),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                // Remove class assignment from all students
                for (final student in students) {
                  final updatedStudent = student.copyWith(classId: null);
                  await appProvider.updateStudent(updatedStudent);
                }

                // Mark class as inactive (soft delete)
                final deletedClass = _schoolClass!.copyWith(isActive: false);
                await appProvider.updateClass(deletedClass);

                if (mounted) {
                  Navigator.pop(context); // Close dialog
                  Navigator.pop(context); // Close edit screen
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Classe "${_schoolClass!.displayName}" supprimée'),
                      backgroundColor: AppTheme.accentRed,
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Erreur: $e'),
                      backgroundColor: AppTheme.accentRed,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.accentRed,
              foregroundColor: Colors.white,
            ),
            child: const Text('Supprimer'),
          ),
        ],
      ),
    );
  }
}
