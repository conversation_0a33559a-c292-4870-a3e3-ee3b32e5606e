import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../providers/app_provider.dart';
import '../../models/class.dart';
import '../../models/student.dart';
import '../../utils/app_theme.dart';
import '../../utils/app_router.dart';
import '../../widgets/class_analytics_card.dart';

class ClassDetailScreen extends StatefulWidget {
  final String classId;

  const ClassDetailScreen({super.key, required this.classId});

  @override
  State<ClassDetailScreen> createState() => _ClassDetailScreenState();
}

class _ClassDetailScreenState extends State<ClassDetailScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  SchoolClass? _schoolClass;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadClass();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadClass() {
    final appProvider = context.read<AppProvider>();
    _schoolClass = appProvider.getClass(widget.classId);
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    if (_schoolClass == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('Classe introuvable')),
        body: const Center(
          child: Text('Cette classe n\'existe pas ou a été supprimée.'),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(_schoolClass!.displayName),
        actions: [
          PopupMenuButton(
            onSelected: (value) => _handleAction(value),
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'edit',
                child: Row(
                  children: [
                    Icon(Icons.edit, size: 16),
                    SizedBox(width: 8),
                    Text('Modifier'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'add_student',
                child: Row(
                  children: [
                    Icon(Icons.person_add, size: 16),
                    SizedBox(width: 8),
                    Text('Ajouter un élève'),
                  ],
                ),
              ),
              if (_schoolClass!.isPrimaryLevel)
                const PopupMenuItem(
                  value: 'add_competency',
                  child: Row(
                    children: [
                      Icon(Icons.stars, size: 16),
                      SizedBox(width: 8),
                      Text('Évaluer compétence'),
                    ],
                  ),
                ),
              if (!_schoolClass!.isPrimaryLevel)
                const PopupMenuItem(
                  value: 'add_grade',
                  child: Row(
                    children: [
                      Icon(Icons.grade, size: 16),
                      SizedBox(width: 8),
                      Text('Ajouter note'),
                    ],
                  ),
                ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Vue d\'ensemble'),
            Tab(text: 'Élèves'),
            Tab(text: 'Analytics'),
          ],
        ),
      ),
      body: Consumer<AppProvider>(
        builder: (context, appProvider, child) {
          return TabBarView(
            controller: _tabController,
            children: [
              _buildOverviewTab(appProvider),
              _buildStudentsTab(appProvider),
              _buildAnalyticsTab(appProvider),
            ],
          );
        },
      ),
    );
  }

  Widget _buildOverviewTab(AppProvider appProvider) {
    final students = appProvider.getClassStudents(_schoolClass!.id);
    final occupancyRate = (students.length / _schoolClass!.maxStudents * 100).round();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header Card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: _schoolClass!.isPrimaryLevel
                              ? AppTheme.successGreen.withOpacity(0.1)
                              : AppTheme.warningOrange.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Icon(
                          _schoolClass!.isPrimaryLevel ? Icons.school : Icons.account_balance,
                          color: _schoolClass!.isPrimaryLevel
                              ? AppTheme.successGreen
                              : AppTheme.warningOrange,
                          size: 32,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _schoolClass!.displayName,
                              style: const TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              _schoolClass!.gradingSystemDisplayName,
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.grey[600],
                              ),
                            ),
                            if (_schoolClass!.academicYear != null) ...[
                              const SizedBox(height: 4),
                              Text(
                                'Année scolaire ${_schoolClass!.academicYear}',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ],
                  ),
                  if (_schoolClass!.description != null) ...[
                    const SizedBox(height: 16),
                    Text(
                      _schoolClass!.description!,
                      style: const TextStyle(fontSize: 16),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Quick Stats
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Élèves',
                  '${students.length}',
                  '/${_schoolClass!.maxStudents}',
                  Icons.people,
                  occupancyRate >= 90
                      ? AppTheme.accentRed
                      : occupancyRate >= 70
                          ? AppTheme.warningOrange
                          : AppTheme.successGreen,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'Capacité',
                  '$occupancyRate%',
                  'utilisée',
                  Icons.pie_chart,
                  occupancyRate >= 90
                      ? AppTheme.accentRed
                      : occupancyRate >= 70
                          ? AppTheme.warningOrange
                          : AppTheme.successGreen,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Details Card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Détails',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  
                  if (_schoolClass!.teacher != null)
                    _buildDetailRow(Icons.person, 'Enseignant', _schoolClass!.teacher!),
                  
                  if (_schoolClass!.classroom != null)
                    _buildDetailRow(Icons.room, 'Salle', _schoolClass!.classroom!),
                  
                  _buildDetailRow(
                    Icons.calendar_today, 
                    'Créée le', 
                    DateFormat('dd/MM/yyyy').format(_schoolClass!.createdAt),
                  ),
                  
                  _buildDetailRow(
                    Icons.update, 
                    'Modifiée le', 
                    DateFormat('dd/MM/yyyy').format(_schoolClass!.updatedAt),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Recent Students
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Élèves récents',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                      TextButton(
                        onPressed: () => _tabController.animateTo(1),
                        child: const Text('Voir tous'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  if (students.isEmpty)
                    Container(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        children: [
                          Icon(Icons.people_outline, size: 48, color: Colors.grey[400]),
                          const SizedBox(height: 8),
                          const Text('Aucun élève dans cette classe'),
                          const SizedBox(height: 8),
                          ElevatedButton(
                            onPressed: () => _handleAction('add_student'),
                            child: const Text('Ajouter un élève'),
                          ),
                        ],
                      ),
                    )
                  else
                    ...students.take(3).map((student) => 
                      ListTile(
                        leading: CircleAvatar(
                          backgroundColor: AppTheme.primaryBlue.withOpacity(0.1),
                          child: Text(
                            student.initials,
                            style: TextStyle(
                              color: AppTheme.primaryBlue,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        title: Text(student.fullName),
                        subtitle: student.age != null 
                            ? Text('${student.age} ans')
                            : null,
                        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                        onTap: () => Navigator.pushNamed(
                          context,
                          AppRouter.studentDetail,
                          arguments: student.id,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),

          if (_schoolClass!.notes != null) ...[
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Notes',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _schoolClass!.notes!,
                      style: const TextStyle(fontSize: 16),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStudentsTab(AppProvider appProvider) {
    final students = appProvider.getClassStudents(_schoolClass!.id);
    
    return Column(
      children: [
        // Header with add button
        Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Text(
                '${students.length} élève${students.length > 1 ? 's' : ''}',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              ElevatedButton.icon(
                onPressed: () => _handleAction('add_student'),
                icon: const Icon(Icons.person_add),
                label: const Text('Ajouter'),
              ),
            ],
          ),
        ),
        
        // Students List
        Expanded(
          child: students.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.people_outline, size: 64, color: Colors.grey[400]),
                      const SizedBox(height: 16),
                      const Text(
                        'Aucun élève dans cette classe',
                        style: TextStyle(fontSize: 18),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Commencez par ajouter des élèves',
                        style: TextStyle(color: Colors.grey[600]),
                      ),
                      const SizedBox(height: 24),
                      ElevatedButton.icon(
                        onPressed: () => _handleAction('add_student'),
                        icon: const Icon(Icons.person_add),
                        label: const Text('Ajouter un élève'),
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: students.length,
                  itemBuilder: (context, index) {
                    final student = students[index];
                    return Card(
                      margin: const EdgeInsets.only(bottom: 8),
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundColor: AppTheme.primaryBlue.withOpacity(0.1),
                          backgroundImage: student.photoPath != null
                              ? FileImage(File(student.photoPath!))
                              : null,
                          child: student.photoPath == null
                              ? Text(
                                  student.initials,
                                  style: TextStyle(
                                    color: AppTheme.primaryBlue,
                                    fontWeight: FontWeight.bold,
                                  ),
                                )
                              : null,
                        ),
                        title: Text(student.fullName),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (student.age != null)
                              Text('${student.age} ans'),
                            if (student.parentEmail != null)
                              Text(
                                student.parentEmail!,
                                style: const TextStyle(fontSize: 12),
                              ),
                          ],
                        ),
                        trailing: PopupMenuButton(
                          onSelected: (value) => _handleStudentAction(value, student),
                          itemBuilder: (context) => [
                            const PopupMenuItem(
                              value: 'view',
                              child: Row(
                                children: [
                                  Icon(Icons.visibility, size: 16),
                                  SizedBox(width: 8),
                                  Text('Voir détails'),
                                ],
                              ),
                            ),
                            if (_schoolClass!.isPrimaryLevel)
                              const PopupMenuItem(
                                value: 'add_competency',
                                child: Row(
                                  children: [
                                    Icon(Icons.stars, size: 16),
                                    SizedBox(width: 8),
                                    Text('Évaluer compétence'),
                                  ],
                                ),
                              ),
                            if (!_schoolClass!.isPrimaryLevel)
                              const PopupMenuItem(
                                value: 'add_grade',
                                child: Row(
                                  children: [
                                    Icon(Icons.grade, size: 16),
                                    SizedBox(width: 8),
                                    Text('Ajouter note'),
                                  ],
                                ),
                              ),
                            const PopupMenuItem(
                              value: 'remove',
                              child: Row(
                                children: [
                                  Icon(Icons.remove_circle, size: 16, color: Colors.red),
                                  SizedBox(width: 8),
                                  Text('Retirer de la classe', style: TextStyle(color: Colors.red)),
                                ],
                              ),
                            ),
                          ],
                        ),
                        onTap: () => Navigator.pushNamed(
                          context,
                          AppRouter.studentDetail,
                          arguments: student.id,
                        ),
                      ),
                    );
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildAnalyticsTab(AppProvider appProvider) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: ClassAnalyticsCard(
        schoolClass: _schoolClass!,
        isExpanded: true,
      ),
    );
  }

  Widget _buildStatCard(String title, String value, String subtitle, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 12,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 12),
          Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _handleAction(String action) {
    switch (action) {
      case 'edit':
        Navigator.pushNamed(
          context,
          AppRouter.editClass,
          arguments: _schoolClass!.id,
        );
        break;
      case 'add_student':
        Navigator.pushNamed(context, AppRouter.addStudent);
        break;
      case 'add_competency':
        Navigator.pushNamed(
          context,
          AppRouter.addCompetencyGrade,
          arguments: {
            'classId': _schoolClass!.id,
          },
        );
        break;
      case 'add_grade':
        Navigator.pushNamed(
          context,
          AppRouter.addGrade,
          arguments: {
            'classId': _schoolClass!.id,
          },
        );
        break;
    }
  }

  void _handleStudentAction(String action, Student student) {
    switch (action) {
      case 'view':
        Navigator.pushNamed(
          context,
          AppRouter.studentDetail,
          arguments: student.id,
        );
        break;
      case 'add_competency':
        Navigator.pushNamed(
          context,
          AppRouter.addCompetencyGrade,
          arguments: {
            'studentId': student.id,
          },
        );
        break;
      case 'add_grade':
        Navigator.pushNamed(
          context,
          AppRouter.addGrade,
          arguments: {
            'studentId': student.id,
          },
        );
        break;
      case 'remove':
        _showRemoveStudentDialog(student);
        break;
    }
  }

  void _showRemoveStudentDialog(Student student) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Retirer l\'élève de la classe'),
        content: Text(
          'Êtes-vous sûr de vouloir retirer ${student.fullName} de la classe "${_schoolClass!.displayName}" ?\n\nL\'élève ne sera pas supprimé, mais ne sera plus assigné à cette classe.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                final updatedStudent = Student(
                  id: student.id,
                  firstName: student.firstName,
                  lastName: student.lastName,
                  photoPath: student.photoPath,
                  dateOfBirth: student.dateOfBirth,
                  parentEmail: student.parentEmail,
                  parentPhone: student.parentPhone,
                  notes: student.notes,
                  createdAt: student.createdAt,
                  updatedAt: DateTime.now(),
                  isActive: student.isActive,
                  classId: null, // Remove class assignment
                );

                final appProvider = context.read<AppProvider>();
                await appProvider.updateStudent(updatedStudent);

                if (mounted) {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('${student.fullName} retiré de la classe'),
                      backgroundColor: AppTheme.successGreen,
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Erreur: $e'),
                      backgroundColor: AppTheme.accentRed,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.accentRed,
              foregroundColor: Colors.white,
            ),
            child: const Text('Retirer'),
          ),
        ],
      ),
    );
  }
}
