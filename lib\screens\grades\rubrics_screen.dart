import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/app_provider.dart';
import '../../models/rubric.dart';
import '../../utils/app_theme.dart';
import '../../utils/app_router.dart';

class RubricsScreen extends StatefulWidget {
  const RubricsScreen({super.key});

  @override
  State<RubricsScreen> createState() => _RubricsScreenState();
}

class _RubricsScreenState extends State<RubricsScreen> {
  String _searchQuery = '';
  String _filterBySubject = 'Tous';
  bool _isLoading = true;
  
  // Sample subjects for filtering
  final List<String> _subjects = [
    'Tous',
    'Français',
    'Mathématiques',
    'Sciences',
    'Histoire-Géographie',
    'Arts Plastiques',
    'EPS'
  ];

  @override
  void initState() {
    super.initState();
    _loadRubrics();
  }

  Future<void> _loadRubrics() async {
    setState(() => _isLoading = true);
    try {
      final appProvider = context.read<AppProvider>();
      await appProvider.loadRubrics();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors du chargement: $e'),
            backgroundColor: AppTheme.accentRed,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Grilles d\'évaluation'),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => _showSearchDialog(context),
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () => _showFilterDialog(context),
          ),
        ],
      ),
      body: Column(
        children: [
          _buildStatsCard(),
          _buildFilterChips(),
          Expanded(
            child: _isLoading 
                ? const Center(child: CircularProgressIndicator())
                : _buildRubricsList(),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => Navigator.pushNamed(context, AppRouter.createRubric),
        icon: const Icon(Icons.add),
        label: const Text('Nouvelle grille'),
      ),
    );
  }

  Widget _buildStatsCard() {
    return Consumer<AppProvider>(
      builder: (context, appProvider, child) {
        final rubrics = appProvider.rubrics ?? [];
        final totalRubrics = rubrics.length;
        final recentlyUsed = rubrics.where((r) => 
          r.lastUsed != null && 
          DateTime.now().difference(r.lastUsed!).inDays <= 30
        ).length;

        return Card(
          margin: const EdgeInsets.all(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Total',
                    totalRubrics.toString(),
                    Icons.grid_view,
                    AppTheme.primaryBlue,
                  ),
                ),
                const VerticalDivider(),
                Expanded(
                  child: _buildStatItem(
                    'Récentes',
                    recentlyUsed.toString(),
                    Icons.schedule,
                    AppTheme.successGreen,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 28),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildFilterChips() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      height: 50,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _subjects.length,
        itemBuilder: (context, index) {
          final subject = _subjects[index];
          final isSelected = _filterBySubject == subject;
          
          return Padding(
            padding: const EdgeInsets.only(right: 8),
            child: FilterChip(
              selected: isSelected,
              label: Text(subject),
              onSelected: (selected) {
                setState(() {
                  _filterBySubject = subject;
                });
              },
              selectedColor: AppTheme.primaryBlue.withOpacity(0.2),
              checkmarkColor: AppTheme.primaryBlue,
            ),
          );
        },
      ),
    );
  }

  Widget _buildRubricsList() {
    return Consumer<AppProvider>(
      builder: (context, appProvider, child) {
        final rubrics = appProvider.rubrics ?? [];
        
        if (rubrics.isEmpty) {
          return _buildEmptyState();
        }

        final filteredRubrics = rubrics.where((rubric) {
          final matchesSearch = _searchQuery.isEmpty ||
              rubric.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
              rubric.subject.toLowerCase().contains(_searchQuery.toLowerCase());
          
          final matchesSubject = _filterBySubject == 'Tous' ||
              rubric.subject == _filterBySubject;

          return matchesSearch && matchesSubject;
        }).toList();

        if (filteredRubrics.isEmpty) {
          return _buildNoResultsState();
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: filteredRubrics.length,
          itemBuilder: (context, index) {
            final rubric = filteredRubrics[index];
            return _buildRubricCard(rubric);
          },
        );
      },
    );
  }

  Widget _buildRubricCard(Rubric rubric) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => _showRubricDetail(rubric),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          rubric.title,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          rubric.subject,
                          style: TextStyle(
                            fontSize: 14,
                            color: AppTheme.primaryBlue,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  PopupMenuButton<String>(
                    onSelected: (value) => _handleRubricAction(value, rubric),
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'view',
                        child: ListTile(
                          leading: Icon(Icons.visibility),
                          title: Text('Voir détails'),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'use',
                        child: ListTile(
                          leading: Icon(Icons.assignment),
                          title: Text('Utiliser'),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'edit',
                        child: ListTile(
                          leading: Icon(Icons.edit),
                          title: Text('Modifier'),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'duplicate',
                        child: ListTile(
                          leading: Icon(Icons.copy),
                          title: Text('Dupliquer'),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: ListTile(
                          leading: Icon(Icons.delete, color: Colors.red),
                          title: Text('Supprimer', style: TextStyle(color: Colors.red)),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              if (rubric.description?.isNotEmpty == true) ...[
                const SizedBox(height: 8),
                Text(
                  rubric.description!,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
              const SizedBox(height: 12),
              Row(
                children: [
                  _buildInfoChip(
                    '${rubric.criteria.length} critères',
                    Icons.checklist,
                    AppTheme.successGreen,
                  ),
                  const SizedBox(width: 8),
                  _buildInfoChip(
                    '${rubric.maxScore} pts max',
                    Icons.grade,
                    AppTheme.warningOrange,
                  ),
                  const SizedBox(width: 8),
                  if (rubric.lastUsed != null)
                    _buildInfoChip(
                      'Utilisée ${_formatLastUsed(rubric.lastUsed!)}',
                      Icons.schedule,
                      AppTheme.primaryBlue,
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoChip(String label, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 10,
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  String _formatLastUsed(DateTime lastUsed) {
    final difference = DateTime.now().difference(lastUsed);
    if (difference.inDays == 0) {
      return 'aujourd\'hui';
    } else if (difference.inDays == 1) {
      return 'hier';
    } else if (difference.inDays < 7) {
      return 'il y a ${difference.inDays} jours';
    } else if (difference.inDays < 30) {
      return 'il y a ${(difference.inDays / 7).round()} semaines';
    } else {
      return 'il y a ${(difference.inDays / 30).round()} mois';
    }
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.grid_view,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          const Text(
            'Aucune grille d\'évaluation',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Créez votre première grille d\'évaluation pour commencer',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => Navigator.pushNamed(context, AppRouter.createRubric),
            icon: const Icon(Icons.add),
            label: const Text('Créer une grille'),
          ),
        ],
      ),
    );
  }

  Widget _buildNoResultsState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          const Text(
            'Aucun résultat',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Essayez de modifier votre recherche ou vos filtres',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _searchQuery = '';
                _filterBySubject = 'Tous';
              });
            },
            child: const Text('Effacer les filtres'),
          ),
        ],
      ),
    );
  }

  void _showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Rechercher'),
        content: TextFormField(
          initialValue: _searchQuery,
          decoration: const InputDecoration(
            hintText: 'Nom de la grille ou matière...',
            prefixIcon: Icon(Icons.search),
          ),
          onChanged: (value) {
            setState(() {
              _searchQuery = value;
            });
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  void _showFilterDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filtrer par matière'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: _subjects.map((subject) {
            return RadioListTile<String>(
              title: Text(subject),
              value: subject,
              groupValue: _filterBySubject,
              onChanged: (value) {
                setState(() {
                  _filterBySubject = value!;
                });
                Navigator.pop(context);
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  void _handleRubricAction(String action, Rubric rubric) {
    switch (action) {
      case 'view':
        _showRubricDetail(rubric);
        break;
      case 'use':
        _useRubric(rubric);
        break;
      case 'edit':
        _editRubric(rubric);
        break;
      case 'duplicate':
        _duplicateRubric(rubric);
        break;
      case 'delete':
        _deleteRubric(rubric);
        break;
    }
  }

  void _showRubricDetail(Rubric rubric) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(rubric.title),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Matière: ${rubric.subject}'),
              if (rubric.description?.isNotEmpty == true) ...[
                const SizedBox(height: 8),
                Text('Description: ${rubric.description}'),
              ],
              const SizedBox(height: 16),
              const Text(
                'Critères d\'évaluation:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              ...rubric.criteria.map((criterion) => Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Text('• ${criterion.name} (${criterion.maxScore} pts)'),
              )),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fermer'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _useRubric(rubric);
            },
            child: const Text('Utiliser'),
          ),
        ],
      ),
    );
  }

  void _useRubric(Rubric rubric) {
    // Navigate to rubric assessment screen
    Navigator.pushNamed(
      context,
      AppRouter.rubricAssessment,
      arguments: {
        'rubricId': rubric.id,
        'studentId': null, // Will be selected in the assessment screen
      },
    );
  }

  void _editRubric(Rubric rubric) {
    Navigator.pushNamed(
      context,
      AppRouter.createRubric,
      arguments: rubric,
    );
  }

  Future<void> _duplicateRubric(Rubric rubric) async {
    try {
      final appProvider = context.read<AppProvider>();
      final duplicatedRubric = rubric.copyWith(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        title: '${rubric.title} (copie)',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        lastUsed: null,
      );
      
      await appProvider.addRubric(duplicatedRubric);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Grille dupliquée avec succès'),
            backgroundColor: AppTheme.successGreen,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la duplication: $e'),
            backgroundColor: AppTheme.accentRed,
          ),
        );
      }
    }
  }

  Future<void> _deleteRubric(Rubric rubric) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirmer la suppression'),
        content: Text('Êtes-vous sûr de vouloir supprimer la grille "${rubric.title}" ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.accentRed,
            ),
            child: const Text('Supprimer'),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      try {
        final appProvider = context.read<AppProvider>();
        await appProvider.deleteRubric(rubric.id);
        
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Grille supprimée avec succès'),
            backgroundColor: AppTheme.successGreen,
          ),
        );
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la suppression: $e'),
            backgroundColor: AppTheme.accentRed,
          ),
        );
      }
    }
  }
}
