import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'services/database_service.dart';
import 'providers/app_provider.dart';
import 'utils/app_theme.dart';
import 'utils/app_router.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize database
  await DatabaseService.init();

  runApp(const GestionClasseApp());
}

class GestionClasseApp extends StatelessWidget {
  const GestionClasseApp({super.key});

  @override
  Widget build(BuildContext context) {
    print('Building GestionClasseApp widget');
    return ChangeNotifierProvider(
      create: (context) {
        print('Creating AppProvider');
        return AppProvider()..initializeApp();
      },
      child: Consumer<AppProvider>(
        builder: (context, appProvider, child) {
          print('Consumer builder called - isLoading: ${appProvider.isLoading}');
          return MaterialApp(
            title: 'isoucklou',
            debugShowCheckedModeBanner: false,
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: appProvider.isDarkMode
                ? ThemeMode.dark
                : ThemeMode.light,
            localizationsDelegates: const [
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: const [
              Locale('fr', 'FR'), // French
              Locale('en', 'US'), // English
            ],
            locale: const Locale('fr', 'FR'),
            initialRoute: AppRouter.dashboard,
            onGenerateRoute: AppRouter.generateRoute,
            builder: (context, child) {
              return MediaQuery(
                data: MediaQuery.of(context).copyWith(
                  textScaler: const TextScaler.linear(
                    1.0,
                  ), // Prevent system text scaling
                ),
                child: child!,
              );
            },
          );
        },
      ),
    );
  }
}
