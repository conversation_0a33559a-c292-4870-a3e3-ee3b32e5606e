import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/app_provider.dart';
import '../../models/class.dart';
import '../../models/student.dart';
import '../../utils/app_theme.dart';
import '../../utils/app_router.dart';

class ClassesScreen extends StatefulWidget {
  const ClassesScreen({super.key});

  @override
  State<ClassesScreen> createState() => _ClassesScreenState();
}

class _ClassesScreenState extends State<ClassesScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _searchQuery = '';
  GradingSystemType? _selectedGradingSystem;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Gestion des classes'),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showSearchDialog,
          ),
          IconButton(
            icon: const Icon(Icons.people),
            onPressed: () => Navigator.pushNamed(context, AppRouter.assignStudents),
            tooltip: 'Gestion des membres',
          ),
          PopupMenuButton<GradingSystemType>(
            icon: const Icon(Icons.filter_list),
            onSelected: (value) {
              setState(() {
                _selectedGradingSystem = value;
              });
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: null,
                child: Text('Toutes les classes'),
              ),
              const PopupMenuItem(
                value: GradingSystemType.primary,
                child: Text('Primaire (Compétences)'),
              ),
              const PopupMenuItem(
                value: GradingSystemType.secondary,
                child: Text('Secondaire (Notes /20)'),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Toutes'),
            Tab(text: 'Primaire'),
            Tab(text: 'Secondaire'),
          ],
        ),
      ),
      body: Consumer<AppProvider>(
        builder: (context, appProvider, child) {
          if (appProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          final classes = appProvider.classes ?? [];
          
          if (classes.isEmpty) {
            return _buildEmptyState();
          }

          return TabBarView(
            controller: _tabController,
            children: [
              _buildClassList(_filterClasses(classes)),
              _buildClassList(_filterClasses(classes
                  .where((c) => c.isPrimaryLevel)
                  .toList())),
              _buildClassList(_filterClasses(classes
                  .where((c) => c.isSecondaryLevel)
                  .toList())),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => Navigator.pushNamed(context, AppRouter.addClass),
        tooltip: 'Créer une classe',
        child: const Icon(Icons.add),
      ),
    );
  }

  List<SchoolClass> _filterClasses(List<SchoolClass> classes) {
    var filteredClasses = classes.where((schoolClass) => schoolClass.isActive);

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filteredClasses = filteredClasses.where((schoolClass) =>
          schoolClass.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          schoolClass.levelDisplayName.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          (schoolClass.teacher?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false));
    }

    // Apply grading system filter
    if (_selectedGradingSystem != null) {
      filteredClasses = filteredClasses
          .where((schoolClass) => schoolClass.gradingSystem == _selectedGradingSystem);
    }

    return filteredClasses.toList();
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.class_,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Aucune classe créée',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Créez votre première classe pour commencer',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => Navigator.pushNamed(context, AppRouter.addClass),
            icon: const Icon(Icons.add),
            label: const Text('Créer une classe'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildClassList(List<SchoolClass> classes) {
    if (classes.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search_off, size: 48, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'Aucune classe trouvée',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
            if (_searchQuery.isNotEmpty || _selectedGradingSystem != null) ...[
              const SizedBox(height: 8),
              TextButton(
                onPressed: () {
                  setState(() {
                    _searchQuery = '';
                    _selectedGradingSystem = null;
                  });
                },
                child: const Text('Effacer les filtres'),
              ),
            ],
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () => context.read<AppProvider>().loadClasses(),
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: classes.length,
        itemBuilder: (context, index) {
          final schoolClass = classes[index];
          return _buildClassCard(schoolClass);
        },
      ),
    );
  }

  Widget _buildClassCard(SchoolClass schoolClass) {
    final appProvider = context.read<AppProvider>();
    final students = appProvider.getClassStudents(schoolClass.id);
    final occupancyRate = (students.length / schoolClass.maxStudents * 100).round();
    
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () => Navigator.pushNamed(
          context,
          AppRouter.classDetail,
          arguments: schoolClass.id,
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: schoolClass.isPrimaryLevel
                          ? AppTheme.successGreen.withOpacity(0.1)
                          : AppTheme.warningOrange.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Icon(
                      schoolClass.isPrimaryLevel ? Icons.school : Icons.account_balance,
                      color: schoolClass.isPrimaryLevel
                          ? AppTheme.successGreen
                          : AppTheme.warningOrange,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          schoolClass.displayName,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          schoolClass.gradingSystemDisplayName,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  PopupMenuButton(
                    onSelected: (value) => _handleClassAction(value, schoolClass),
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit, size: 16),
                            SizedBox(width: 8),
                            Text('Modifier'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'students',
                        child: Row(
                          children: [
                            Icon(Icons.people, size: 16),
                            SizedBox(width: 8),
                            Text('Élèves'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'assign',
                        child: Row(
                          children: [
                            Icon(Icons.person_add, size: 16),
                            SizedBox(width: 8),
                            Text('Assigner des élèves'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'duplicate',
                        child: Row(
                          children: [
                            Icon(Icons.copy, size: 16),
                            SizedBox(width: 8),
                            Text('Dupliquer'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'analytics',
                        child: Row(
                          children: [
                            Icon(Icons.analytics, size: 16),
                            SizedBox(width: 8),
                            Text('Analytics'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, size: 16, color: Colors.red),
                            SizedBox(width: 8),
                            Text('Supprimer', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Info Row
              Row(
                children: [
                  _buildInfoChip(
                    Icons.people,
                    '${students.length}/${schoolClass.maxStudents}',
                    occupancyRate >= 90
                        ? AppTheme.accentRed
                        : occupancyRate >= 70
                            ? AppTheme.warningOrange
                            : AppTheme.successGreen,
                  ),
                  const SizedBox(width: 8),
                  if (schoolClass.teacher != null)
                    _buildInfoChip(
                      Icons.person,
                      schoolClass.teacher!,
                      AppTheme.primaryBlue,
                    ),
                  const SizedBox(width: 8),
                  if (schoolClass.classroom != null)
                    _buildInfoChip(
                      Icons.room,
                      schoolClass.classroom!,
                      AppTheme.secondaryBlue,
                    ),
                ],
              ),
              
              if (schoolClass.description != null) ...[
                const SizedBox(height: 12),
                Text(
                  schoolClass.description!,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoChip(IconData icon, String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Rechercher une classe'),
        content: TextField(
          autofocus: true,
          decoration: const InputDecoration(
            hintText: 'Nom, niveau, enseignant...',
            prefixIcon: Icon(Icons.search),
          ),
          onChanged: (value) {
            setState(() {
              _searchQuery = value;
            });
          },
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                _searchQuery = '';
              });
              Navigator.pop(context);
            },
            child: const Text('Effacer'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _handleClassAction(String action, SchoolClass schoolClass) {
    switch (action) {
      case 'edit':
        Navigator.pushNamed(
          context,
          AppRouter.editClass,
          arguments: schoolClass.id,
        );
        break;
      case 'students':
        Navigator.pushNamed(
          context,
          AppRouter.students,
          arguments: schoolClass.id,
        );
        break;
      case 'assign':
        Navigator.pushNamed(
          context,
          AppRouter.assignStudents,
          arguments: schoolClass.id,
        );
        break;
      case 'duplicate':
        _duplicateClass(schoolClass);
        break;
      case 'analytics':
        _showClassAnalytics(schoolClass);
        break;
      case 'delete':
        _showDeleteConfirmation(schoolClass);
        break;
    }
  }

  void _showClassAnalytics(SchoolClass schoolClass) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.8,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) {
          return Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: Column(
              children: [
                // Handle
                Container(
                  margin: const EdgeInsets.symmetric(vertical: 8),
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                // Header
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Text(
                        'Analytics - ${schoolClass.displayName}',
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: const Icon(Icons.close),
                      ),
                    ],
                  ),
                ),
                // Content
                Expanded(
                  child: SingleChildScrollView(
                    controller: scrollController,
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Consumer<AppProvider>(
                      builder: (context, appProvider, child) {
                        return Column(
                          children: [
                            // Use the ClassAnalyticsCard widget we created earlier
                            // ClassAnalyticsCard(
                            //   schoolClass: schoolClass,
                            //   isExpanded: true,
                            // ),
                            // Placeholder for now
                            const Card(
                              child: Padding(
                                padding: EdgeInsets.all(20),
                                child: Text('Analytics détaillées à venir'),
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  void _duplicateClass(SchoolClass schoolClass) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Dupliquer la classe'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Êtes-vous sûr de vouloir dupliquer la classe "${schoolClass.displayName}" ?',
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppTheme.primaryBlue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: AppTheme.primaryBlue.withOpacity(0.3)),
              ),
              child: Row(
                children: [
                  Icon(Icons.info, color: AppTheme.primaryBlue, size: 20),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      'Une copie sera créée avec les mêmes paramètres. Vous pourrez la modifier par la suite.',
                      style: TextStyle(fontSize: 12),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                final appProvider = context.read<AppProvider>();
                await appProvider.duplicateClass(schoolClass.id);
                
                if (mounted) {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Classe "${schoolClass.displayName}" dupliquée avec succès'),
                      backgroundColor: AppTheme.successGreen,
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Erreur lors de la duplication: $e'),
                      backgroundColor: AppTheme.accentRed,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryBlue,
              foregroundColor: Colors.white,
            ),
            child: const Text('Dupliquer'),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(SchoolClass schoolClass) {
    final appProvider = context.read<AppProvider>();
    final students = appProvider.getClassStudents(schoolClass.id);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Supprimer la classe'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Êtes-vous sûr de vouloir supprimer la classe "${schoolClass.displayName}" ?'),
            if (students.isNotEmpty) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppTheme.warningOrange.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppTheme.warningOrange.withOpacity(0.3)),
                ),
                child: Row(
                  children: [
                    Icon(Icons.warning, color: AppTheme.warningOrange, size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        '${students.length} élève(s) sont assigné(s) à cette classe. Ils ne seront pas supprimés mais ne seront plus assignés à aucune classe.',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppTheme.warningOrange,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                // Update students to remove class assignment
                for (final student in students) {
                  final updatedStudent = Student(
                    id: student.id,
                    firstName: student.firstName,
                    lastName: student.lastName,
                    photoPath: student.photoPath,
                    dateOfBirth: student.dateOfBirth,
                    parentEmail: student.parentEmail,
                    parentPhone: student.parentPhone,
                    notes: student.notes,
                    createdAt: student.createdAt,
                    updatedAt: DateTime.now(),
                    isActive: student.isActive,
                    classId: null, // Remove class assignment
                  );
                  await appProvider.updateStudent(updatedStudent);
                }

                // Delete the class (mark as inactive)
                final updatedClass = schoolClass.copyWith(isActive: false);
                await appProvider.updateClass(updatedClass);

                if (mounted) {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Classe "${schoolClass.displayName}" supprimée'),
                      backgroundColor: AppTheme.successGreen,
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Erreur lors de la suppression: $e'),
                      backgroundColor: AppTheme.accentRed,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.accentRed,
              foregroundColor: Colors.white,
            ),
            child: const Text('Supprimer'),
          ),
        ],
      ),
    );
  }
}
