import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/app_provider.dart';
import '../../utils/app_theme.dart';
import '../../utils/app_router.dart';

class EnhancedDashboardScreen extends StatefulWidget {
  const EnhancedDashboardScreen({super.key});

  @override
  State<EnhancedDashboardScreen> createState() =>
      _EnhancedDashboardScreenState();
}

class _EnhancedDashboardScreenState extends State<EnhancedDashboardScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Tableau de bord'),
        actions: [
          // Theme toggle with better visibility
          Consumer<AppProvider>(
            builder: (context, appProvider, child) {
              return Container(
                margin: const EdgeInsets.only(right: 8),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: IconButton(
                  icon: Icon(
                    appProvider.isDarkMode ? Icons.light_mode : Icons.dark_mode,
                    color: Colors.white,
                  ),
                  onPressed: () {
                    appProvider.toggleTheme();
                    // Show feedback
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          appProvider.isDarkMode
                              ? 'Mode sombre activé'
                              : 'Mode clair activé',
                        ),
                        duration: const Duration(milliseconds: 1500),
                        backgroundColor: AppTheme.primaryGreen,
                      ),
                    );
                  },
                  tooltip: appProvider.isDarkMode
                      ? 'Passer en mode clair'
                      : 'Passer en mode sombre',
                ),
              );
            },
          ),
          // User manual with enhanced styling
          Container(
            margin: const EdgeInsets.only(right: 8),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.15),
              borderRadius: BorderRadius.circular(20),
            ),
            child: IconButton(
              icon: const Icon(Icons.help_outline, color: Colors.white),
              onPressed: () {
                Navigator.pushNamed(context, AppRouter.userManual);
              },
              tooltip: 'Manuel d\'utilisation',
            ),
          ),
          // Settings with enhanced styling
          Container(
            margin: const EdgeInsets.only(right: 8),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.15),
              borderRadius: BorderRadius.circular(20),
            ),
            child: IconButton(
              icon: const Icon(Icons.settings, color: Colors.white),
              onPressed: () {
                Navigator.pushNamed(context, AppRouter.settings);
              },
              tooltip: 'Paramètres',
            ),
          ),
          // Notifications
          Container(
            margin: const EdgeInsets.only(right: 8),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.15),
              borderRadius: BorderRadius.circular(20),
            ),
            child: IconButton(
              icon: const Icon(
                Icons.notifications_outlined,
                color: Colors.white,
              ),
              onPressed: () => _showNotifications(context),
              tooltip: 'Notifications',
            ),
          ),
          // Search
          Container(
            margin: const EdgeInsets.only(right: 16),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.15),
              borderRadius: BorderRadius.circular(20),
            ),
            child: IconButton(
              icon: const Icon(Icons.search, color: Colors.white),
              onPressed: () => _showGlobalSearch(context),
              tooltip: 'Recherche globale',
            ),
          ),
        ],
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Consumer<AppProvider>(
          builder: (context, appProvider, child) {
            if (appProvider.isLoading) {
              return _buildLoadingScreen();
            }

            return RefreshIndicator(
              onRefresh: () => _refreshData(appProvider),
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildWelcomeCard(),
                    const SizedBox(height: 20),
                    _buildQuickStats(appProvider),
                    const SizedBox(height: 20),
                    _buildQuickActions(),
                    const SizedBox(height: 20),
                    _buildRecentActivity(appProvider),
                    const SizedBox(height: 20),
                    _buildUpcomingTasks(),
                  ],
                ),
              ),
            );
          },
        ),
      ),
      floatingActionButton: _buildSpeedDial(),
    );
  }

  Widget _buildLoadingScreen() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryGreen),
          ),
          const SizedBox(height: 24),
          Text(
            'Chargement...',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(color: Colors.grey[600]),
          ),
        ],
      ),
    );
  }

  Widget _buildWelcomeCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [
              AppTheme.primaryGreen,
              AppTheme.primaryGreen.withValues(alpha: 0.7),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Bonjour ! 👋',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Gérez vos classes efficacement',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Colors.white.withValues(alpha: 0.9),
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.school_outlined,
              size: 48,
              color: Colors.white.withOpacity(0.8),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStats(AppProvider appProvider) {
    final classes = appProvider.classes ?? [];
    final students = appProvider.students ?? [];
    final activeClasses = classes.where((c) => c.isActive).length;
    final activeStudents = students.where((s) => s.isActive).length;

    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'Classes',
            activeClasses.toString(),
            Icons.class_outlined,
            AppTheme.primaryBlue,
            () => Navigator.pushNamed(context, AppRouter.classes),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            'Élèves',
            activeStudents.toString(),
            Icons.people_outline,
            AppTheme.successGreen,
            () => Navigator.pushNamed(context, AppRouter.students),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            'Présences',
            appProvider.calculateOverallAttendancePercentage(),
            Icons.check_circle_outline,
            AppTheme.warningOrange,
            () => Navigator.pushNamed(context, AppRouter.attendance),
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: color.withOpacity(0.2)),
          ),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: color,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                title,
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Actions rapides',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        GridView.count(
          crossAxisCount: 2,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 2.5,
          children: [
            _buildActionButton(
              'Nouvelle classe',
              Icons.add_circle_outline,
              AppTheme.primaryBlue,
              () => Navigator.pushNamed(context, AppRouter.addClass),
            ),
            _buildActionButton(
              'Ajouter élève',
              Icons.person_add_outlined,
              AppTheme.successGreen,
              () => Navigator.pushNamed(context, AppRouter.addStudent),
            ),
            _buildActionButton(
              'Prendre présences',
              Icons.check_box_outlined,
              AppTheme.warningOrange,
              () => Navigator.pushNamed(context, AppRouter.dailyAttendance),
            ),
            _buildActionButton(
              'Évaluation avancée',
              Icons.assessment_outlined,
              AppTheme.primaryBlue,
              () => Navigator.pushNamed(context, AppRouter.advancedGrading),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionButton(
    String title,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: color.withOpacity(0.1),
        foregroundColor: color,
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(color: color.withOpacity(0.3)),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 20),
          const SizedBox(width: 8),
          Flexible(
            child: Text(
              title,
              style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentActivity(AppProvider appProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Activité récente',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton(
                  onPressed: () {}, // TODO: Navigate to full activity log
                  child: const Text('Voir tout'),
                ),
              ],
            ),
            const SizedBox(height: 12),
            // TODO: Implement actual recent activity data
            _buildActivityItem(
              'Nouvelle classe "CM2-A" créée',
              'Il y a 2 heures',
              Icons.class_,
              AppTheme.primaryBlue,
            ),
            _buildActivityItem(
              'Élève "Marie Dupont" ajoutée',
              'Il y a 4 heures',
              Icons.person_add,
              AppTheme.successGreen,
            ),
            _buildActivityItem(
              'Présences prises pour CP-B',
              'Hier',
              Icons.check_circle,
              AppTheme.warningOrange,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityItem(
    String title,
    String time,
    IconData icon,
    Color color,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 16),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
                ),
                Text(
                  time,
                  style: Theme.of(
                    context,
                  ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUpcomingTasks() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Tâches à venir',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            // TODO: Implement actual upcoming tasks
            ListTile(
              leading: Icon(Icons.assignment, color: AppTheme.warningOrange),
              title: const Text('Évaluations trimestrielles'),
              subtitle: const Text('Dans 3 jours'),
              trailing: Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () {}, // TODO: Navigate to evaluations
            ),
            ListTile(
              leading: Icon(Icons.meeting_room, color: AppTheme.primaryBlue),
              title: const Text('Réunion parents CM2'),
              subtitle: const Text('Vendredi 15h00'),
              trailing: Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () {}, // TODO: Navigate to meetings
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSpeedDial() {
    return FloatingActionButton.extended(
      onPressed: () => _showQuickCreateMenu(),
      icon: const Icon(Icons.add),
      label: const Text('Créer'),
    );
  }

  Future<void> _refreshData(AppProvider appProvider) async {
    await Future.wait([appProvider.loadClasses(), appProvider.loadStudents()]);
  }

  void _showNotifications(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Notifications'),
        content: const Text('Aucune nouvelle notification'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  void _showGlobalSearch(BuildContext context) {
    showSearch(context: context, delegate: GlobalSearchDelegate());
  }

  void _showExportOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Options d\'export',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.table_view),
              title: const Text('Export Excel'),
              subtitle: const Text('Toutes les données en format Excel'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Implement Excel export
              },
            ),
            ListTile(
              leading: const Icon(Icons.picture_as_pdf),
              title: const Text('Rapport PDF'),
              subtitle: const Text('Rapport formaté pour impression'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Implement PDF export
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showQuickCreateMenu() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Création rapide',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: Icon(Icons.class_, color: AppTheme.primaryBlue),
              title: const Text('Nouvelle classe'),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(context, AppRouter.addClass);
              },
            ),
            ListTile(
              leading: Icon(Icons.person_add, color: AppTheme.successGreen),
              title: const Text('Nouvel élève'),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(context, AppRouter.addStudent);
              },
            ),
          ],
        ),
      ),
    );
  }
}

class GlobalSearchDelegate extends SearchDelegate {
  @override
  List<Widget>? buildActions(BuildContext context) {
    return [
      IconButton(
        onPressed: () {
          query = '';
        },
        icon: const Icon(Icons.clear),
      ),
    ];
  }

  @override
  Widget? buildLeading(BuildContext context) {
    return IconButton(
      onPressed: () {
        close(context, null);
      },
      icon: const Icon(Icons.arrow_back),
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    // TODO: Implement actual search results
    return const Center(child: Text('Résultats de recherche - À implémenter'));
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    // TODO: Implement search suggestions
    return const Center(
      child: Text('Suggestions de recherche - À implémenter'),
    );
  }
}
